# Copyright 2023 Acronis
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
# implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import collections

from neutron_lib.api.definitions import portbindings
from neutron_lib import constants as lib_const
from neutron_lib.db import api as db_api
from neutron_lib import exceptions as n_exc
from neutron_lib.plugins import constants as plugin_constants
from neutron_lib.plugins import directory
from neutron_lib.plugins import utils as p_utils
from oslo_log import log as logging
import oslo_messaging

from neutron._i18n import _
from neutron.common import rpc as n_rpc
from neutron.extensions import port_check
from neutron.objects import ports as port_obj
from neutron.objects import provisioning_blocks as pb_obj
from neutron.plugins.ml2 import models as ml2_models

LOG = logging.getLogger(__name__)

TOPIC_PORT_CHECK = 'q-port-check'

CHECKS = frozenset((
    'status',
    'binding',
    'provisioning',
    # OVS agent checks:
    'openvswitch',
    'firewall',
    'service_datapath',
    'private_floating',
    'metadata_path',
    'ra_speaker',
    # DHCP agent checks:
    'dhcp',
    # L3 agent checks:
))


class Reports(list):
    def add(self, msg, *args):
        if args:
            msg = msg % args
        self.append(msg)


class PortCheckResult(object):
    def __init__(self):
        self._dict = collections.defaultdict(Reports)

    def to_dict(self):
        return dict(self._dict)

    def __str__(self):
        return '%s(%s)' % (self.__class__.__name__, self.to_dict())

    def update(self, params):
        for key, items in params.items():
            if key not in CHECKS:
                raise ValueError('Unexpected check name: %s' % key)
            self._dict[key].extend(items)

    def __getitem__(self, item):
        if item not in CHECKS:
            raise KeyError(item)
        return self._dict[item]


class OvsAgentRpcApi(object):
    def __init__(self):
        target = oslo_messaging.Target(topic=TOPIC_PORT_CHECK)
        self.client = n_rpc.get_client(target)

    def ports_check(self, context, ports, host):
        cctxt = self.client.prepare(server=host)
        ports = [port.obj_to_primitive() for port in ports]
        # NOTE: currently port checking is synchronous operation. Maybe
        # it worth to reconsider the approach and make it asynchronous.
        # But for that it will be necessary to introduce the management
        # of the port check operation status.
        return cctxt.call(context, 'ports_check', ports=ports)

    def flow_trace(self, context, port, host, **kwargs):
        cctxt = self.client.prepare(server=host)
        port = port.obj_to_primitive()
        return cctxt.call(context, 'flow_trace', port=port, **kwargs)


class PortCheckPlugin(port_check.PortCheckPluginBase):
    """PortCheckPlugin which supports port checking functionality."""

    supported_extension_aliases = ['port-check']

    def __init__(self):
        self.ovs_agent_rpc = OvsAgentRpcApi()
        self.plugin = directory.get_plugin()
        self.l3_plugin = directory.get_plugin(plugin_constants.L3)
        LOG.info('Port-check plugin loaded')

    def port_check(self, context, port_id=None, host_id=None):
        LOG.info('Getting port check info')
        if not port_id and not host_id:
            raise n_exc.BadRequest(resource='check',
                                   msg=_("port_id or host_id needed"))
        if port_id and host_id:
            raise n_exc.BadRequest(resource='check',
                                   msg=_("Only need port_id or host_id"))

        if port_id:
            port = port_obj.Port.get_object(context, id=port_id)
            if not port:
                raise n_exc.PortNotFound(port_id=port_id)
            result_map = self.ports_check(context, [port])
            result = result_map[port_id].to_dict()
        else:
            ports = port_obj.Port.get_ports_by_binding_type_and_host(
                context, 'ovs', host_id)
            result_map = self.ports_check(context, ports)
            result = {}
            for port_id, res in result_map.items():
                result[port_id] = []
                for check_name, reports in res.to_dict().items():
                    if len(reports) > 0:
                        result[port_id].append('check %s wrong' % check_name)
        return {'port_check': result}

    def _validate_and_set_flow_trace_parameters(self, port, **kwargs):
        direction = kwargs.get('direction', None)
        physical_dev = kwargs.get('physical_dev', None)
        protocol = kwargs.get('protocol')
        arp_op = kwargs.get('arp_op', 1)
        ip_dst = kwargs.get('ip_dst', None)
        ip_src = kwargs.get('ip_src', None)
        mac_dst = kwargs.get('mac_dst', None)
        mac_src = kwargs.get('mac_src', None)

        if not direction:
            msg = _('flow trace need direction')
            raise n_exc.BadRequest(resource='Trace', msg=msg)

        if direction == 'ingress':
            if not physical_dev or not ip_src or not mac_src:
                msg = _('ingress trace need physical_dev, '
                        'source ip and source mac.')
                raise n_exc.BadRequest(resource='Trace', msg=msg)
            if not ip_dst:
                for ip in port['fixed_ips']:
                    kwargs['ip_dst'] = str(ip['ip_address'])
                    break
            if not mac_dst:
                kwargs['mac_dst'] = '00:00:00:00:00:00'
        else:
            if protocol == 'arp' and arp_op == 1:
                if not ip_dst:
                    msg = _('egress arp need dest ip')
                    raise n_exc.BadRequest(resource='Trace', msg=msg)
            else:
                if not ip_dst or not mac_dst:
                    msg = _('egress trace need dest ip and dest mac')
                    raise n_exc.BadRequest(resource='Trace', msg=msg)

            if not ip_src:
                for ip in port['fixed_ips']:
                    kwargs['ip_src'] = str(ip['ip_address'])
                    break
            if not mac_src:
                kwargs['mac_src'] = str(port['mac_address'])
            if protocol == 'arp' and arp_op == 1:
                kwargs['mac_dst'] = '00:00:00:00:00:00'
        return kwargs

    def flow_trace(self, context, port_id, **kwargs):
        LOG.info('Getting port trace info')
        port = port_obj.Port.get_object(context, id=port_id)
        if not port:
            raise n_exc.PortNotFound(port_id=port_id)
        if not port.device_owner.startswith(
                lib_const.DEVICE_OWNER_COMPUTE_PREFIX):
            msg = _('Flow trace only support compute port')
            raise n_exc.BadRequest(resource='Trace', msg=msg)

        binding = p_utils.get_port_binding_by_status_and_host(
            port.bindings, lib_const.ACTIVE, raise_if_not_found=True)
        if not binding.host:
            return

        kwargs = self._validate_and_set_flow_trace_parameters(port, **kwargs)
        trace_res = self.ovs_agent_rpc.flow_trace(
            context, port, binding.host, **kwargs)
        return {'flow_trace': trace_res}

    def ports_check(self, context, ports):
        result_map = collections.defaultdict(PortCheckResult)
        self.check_ports_status(context, ports, result_map)
        self.check_provisioning_blocks(context, ports, result_map)
        self.check_l2_ports_bindings(context, ports, result_map)
        self.check_dhcp(context, ports, result_map)
        return result_map

    def check_provisioning_blocks(self, context, ports, result_map):
        pblocks = pb_obj.ProvisioningBlock.get_objects(context)
        entities_map = collections.defaultdict(list)
        for pb in pblocks:
            entities_map[pb.standard_attr_id].append(pb.entity)
        for port in ports:
            reports = result_map[port.id]['provisioning']
            entities = entities_map.get(port.db_obj.standard_attr.id, [])
            for entity in entities:
                reports.add('Port provisioning is not completed by %s agent',
                            entity)

    def check_ports_status(self, context, ports, result_map):
        for port in ports:
            reports = result_map[port.id]['status']
            binding = p_utils.get_port_binding_by_status_and_host(
                port.bindings, lib_const.ACTIVE)
            # Unbinded port has always DOWN status.
            # Report status issue only if the port is binded and not ACTIVE:
            if binding and binding.host and port.status != lib_const.ACTIVE:
                # Even if the port is binded to the host the port with
                # device_owner=network:floatingip_agent_gateway may be in the
                # DOWN state if no VM is connected to the router internal
                # networks:
                if port.device_owner == lib_const.DEVICE_OWNER_AGENT_GW:
                    # skip for now a real check of whether
                    # the port should be ACTIVE or can be DOWN to avoid false
                    # positive fails
                    continue
                reports.add('Port status is %s', port.status)

    def _get_agents(self, context, agent_type):
        filters = {'agent_type': [agent_type]}
        agents = self.l3_plugin.get_agent_objects(context, filters=filters)
        agents = [agent for agent in agents if agent.is_active]
        host_agent_map = dict((agent.host, agent) for agent in agents)
        return host_agent_map

    def check_l2_ports_bindings(self, context, ports, result_map):
        ports_by_host = collections.defaultdict(list)

        dvr_bindings = collections.defaultdict(dict)
        router_hosts = collections.defaultdict(list)
        l2_agents = self._get_agents(context, lib_const.AGENT_TYPE_OVS)
        l3_agents = self._get_agents(context, lib_const.AGENT_TYPE_L3)

        # optimization for `openstack port check` CLI command:
        if (len(ports) != 1 or
                ports[0].device_owner == lib_const.DEVICE_OWNER_DVR_INTERFACE):
            with db_api.CONTEXT_READER.using(context):
                query = context.session.query(
                    ml2_models.DistributedPortBinding)
                for binding in query.all():
                    dvr_bindings[binding.port_id][binding.host] = binding

            for host, agent in l3_agents.items():
                router_ids = self.l3_plugin._get_router_ids_for_agent(
                    context, agent, None)
                for router_id in router_ids:
                    router_hosts[router_id].append(host)

        for port in ports:
            reports = result_map[port.id]['binding']
            inactive_binding = p_utils.get_port_binding_by_status_and_host(
                port.bindings, lib_const.INACTIVE)
            if inactive_binding:
                reports.add('INACTIVE port binding on host %s',
                            inactive_binding.host)
            binding = p_utils.get_port_binding_by_status_and_host(
                port.bindings, lib_const.ACTIVE)
            if not binding:
                # ACTIVE port binding should always present.
                # If not then something critical happened.
                reports.add('ACTIVE port binding not found')
                continue

            bindings = []
            if port.device_owner == lib_const.DEVICE_OWNER_DVR_INTERFACE:
                for host in router_hosts[port.device_id]:
                    binding = dvr_bindings[port.id].get(host)
                    if not binding:
                        reports.add('DVR port is not bound on host %s', host)
                    elif binding.status != lib_const.ACTIVE:
                        reports.add('DVR port binding is %s on host %s',
                                    binding.status, host)
                    else:
                        bindings.append(binding)
            else:
                bindings.append(binding)

            for binding in bindings:
                # Skip bindings without host
                if not binding.host:
                    continue
                # Neutron ovs agent may emit log errors if the device_id of the
                # port is "reserved_dhcp_port" because the ovs port has been
                # removed from the br-int bridge. We report this issue in DHCP
                # port checking step and skip it here:
                if port.device_id == lib_const.DEVICE_ID_RESERVED_DHCP_PORT:
                    continue
                if binding.vif_type == portbindings.VIF_TYPE_UNBOUND:
                    reports.add('Port is unbound')
                elif binding.vif_type == portbindings.VIF_TYPE_BINDING_FAILED:
                    reports.add('Port binding failed')
                else:
                    # Even if the port is binded to the host it can be in the
                    # DOWN state in some cases, i.e. floatingip_agent_gateway
                    # port. If the port is in the DOWN state there is no sense
                    # in checking the firewall logic: such a check will most
                    # likely fail or generate warning in the neutron-ovs-agent
                    if port.status != lib_const.ACTIVE:
                        continue
                    ports_by_host[binding.host].append(port)

        for host, ports in ports_by_host.items():
            if host not in l2_agents:
                # L2 agent is not alive:
                continue
            ovs_result = self.ovs_agent_rpc.ports_check(context, ports, host)
            for port in ports:
                result_map[port.id].update(ovs_result[port.id])

    def check_dhcp(self, context, ports, result_map):
        for port in ports:
            if port.device_owner != lib_const.DEVICE_OWNER_DHCP:
                continue
            # Currently just check reserved dhcp ports, it's unacceptable
            # if such port is found:
            if port.device_id == lib_const.DEVICE_ID_RESERVED_DHCP_PORT:
                result_map[port.id]['dhcp'].add(
                    '%r device_id is unacceptable', port.device_id)
