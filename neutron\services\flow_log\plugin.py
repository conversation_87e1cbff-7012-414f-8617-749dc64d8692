#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.callbacks import registry
from neutron_lib.objects import exceptions as obj_exc
from neutron_lib.plugins import directory
from oslo_db import exception as db_exc
from oslo_log import log as logging

from neutron.api.rpc.callbacks import events as rpc_events
from neutron.api.rpc.callbacks.producer import registry as rpc_registry
from neutron.api.rpc.callbacks import resources
from neutron.api.rpc.handlers import resources_rpc
from neutron.db import _resource_extend as resource_extend
from neutron.db import api as db_api
from neutron.db import db_base_plugin_common
from neutron.extensions import _flow_log as api_def
from neutron.extensions import flow_log as flow_log_ext
from neutron.objects import base as base_obj
from neutron.objects import flow_log as flow_log_obj
from neutron.services.flow_log.common import exceptions as flow_log_exc

LOG = logging.getLogger(__name__)


@resource_extend.has_resource_extenders
@registry.has_registry_receivers
class FlowLogPlugin(flow_log_ext.FlowLogPluginBase):
    supported_extension_aliases = [api_def.ALIAS]

    __native_pagination_support = True
    __native_sorting_support = True
    __filter_validation_support = True

    def __init__(self):
        super(FlowLogPlugin, self).__init__()
        self.core_plugin = directory.get_plugin()
        self.push_api = resources_rpc.ResourcesPushRpcApi()

        rpc_registry.provide(self._get_flow_log_cb, resources.FLOW_LOG)

    @staticmethod
    def _get_flow_log_cb(resource, flow_log_id, **kwargs):
        context = kwargs.get('context')
        if context is None:
            LOG.warning(
                'Received %(resource)s %(flow_log_id)s without context',
                {'resource': resource, 'flow_log_id': flow_log_id})
            return

        flow_log = flow_log_obj.FlowLog.get_object(context, id=flow_log_id)
        return flow_log

    @db_base_plugin_common.convert_result_to_dict
    def create_flow_log(self, context, flow_log):
        log_data = flow_log['flow_log']
        log_data.pop('tenant_id', None)

        try:
            with db_api.context_manager.writer.using(context):
                obj = flow_log_obj.FlowLog(context, **log_data)
                obj.create()
        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            raise flow_log_exc.FlowLogCreateError(error=e)

        self.push_api.push(context, [obj], rpc_events.CREATED)
        return obj

    @db_base_plugin_common.convert_result_to_dict
    def update_flow_log(self, context, id, flow_log):
        log_data = flow_log['flow_log']
        obj = self._get_flow_log(context, id)

        try:
            with db_api.context_manager.writer.using(context):
                obj.update_fields(log_data)
                obj.update()
        except db_exc.DBReferenceError:
            raise flow_log_exc.FlowLogUpdateError(id=id)
        self.push_api.push(context, [obj], rpc_events.UPDATED)
        return obj

    def delete_flow_log(self, context, id):
        flow_log = self._get_flow_log(context, id)
        with db_api.context_manager.writer.using(context):
            for obj_type, obj_class in \
                    flow_log_obj.FlowLog.binding_models.items():
                pager = base_obj.Pager(limit=1)
                binding_obj = obj_class.get_objects(
                    context, flow_log_id=id, _pager=pager)
                if binding_obj:
                    raise flow_log_exc.FlowLogInUse(
                        log_id=id, object_type=obj_type,
                        object_id=binding_obj[0]['%s_id' % obj_type])
            flow_log.delete()
        self.push_api.push(context, [flow_log], rpc_events.DELETED)

    @db_base_plugin_common.filter_fields
    @db_base_plugin_common.convert_result_to_dict
    def get_flow_log(self, context, id, fields=None):
        flow_log = self._get_flow_log(context, id)
        return flow_log

    @db_base_plugin_common.filter_fields
    @db_base_plugin_common.convert_result_to_dict
    def get_flow_logs(self, context, filters=None, fields=None, sorts=None,
                      limit=None, marker=None, page_reverse=False):
        filters = filters or {}
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        flow_logs = flow_log_obj.FlowLog.get_objects(
            context, _pager=pager, **filters)
        return flow_logs

    def _get_flow_log(self, context, id):
        flow_log = flow_log_obj.FlowLog.get_object(context, id=id)
        if not flow_log:
            raise flow_log_exc.FlowLogNotFound(id=id)
        return flow_log
