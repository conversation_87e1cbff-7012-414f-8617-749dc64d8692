#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import sqlalchemy as sa

from neutron_lib.db import constants as db_const
from neutron_lib.db import model_base

from neutron.db import models_v2
from neutron.db import standard_attr
from neutron.extensions import _flow_log as apidef


class FlowLog(standard_attr.HasStandardAttributes,
              model_base.BASEV2, model_base.HasId,
              model_base.HasProject):
    __tablename__ = 'flow_logs'

    name = sa.Column(sa.String(db_const.NAME_FIELD_SIZE), nullable=True)
    collection_interval = sa.Column(sa.Integer(), nullable=True)
    enabled = sa.Column(sa.<PERSON>an(), nullable=True)
    # priority = sa.Column(sa.Integer(), nullable=True)
    # resource_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
    #                         nullable=False)
    # resource_type = sa.Column(sa.Enum('port', 'network',
    #                                   name='flow_log_resource_type'),
    #                           nullable=False)
    traffic_type = sa.Column(sa.Enum('all', 'accept', 'reject',
                                     name='flow_log_traffic_type'),
                             nullable=False)

    api_collections = [apidef.FLOW_LOGS]


class FlowLogPortBinding(model_base.BASEV2):
    __tablename__ = 'flow_log_port_bindings'
    flow_log_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
                            sa.ForeignKey('flow_logs.id', ondelete='CASCADE'),
                            nullable=False,
                            primary_key=True)
    port_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
                        sa.ForeignKey('ports.id', ondelete='CASCADE'),
                        nullable=False,
                        unique=True,
                        primary_key=True)
    revises_on_change = ('port',)
    port = sa.orm.relationship(
        models_v2.Port, load_on_pending=True,
        backref=sa.orm.backref("flow_log_binding", uselist=False,
                               cascade='delete', lazy='joined'))


class FlowLogNetworkBinding(model_base.BASEV2):
    __tablename__ = 'flow_log_network_bindings'
    flow_log_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
                            sa.ForeignKey('flow_logs.id', ondelete='CASCADE'),
                            nullable=False,
                            primary_key=True)
    network_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
                           sa.ForeignKey('networks.id', ondelete='CASCADE'),
                           nullable=False,
                           unique=True,
                           primary_key=True)
    revises_on_change = ('network',)
    network = sa.orm.relationship(
        models_v2.Network, load_on_pending=True,
        backref=sa.orm.backref("flow_log_binding", uselist=False,
                               cascade='delete', lazy='joined'))
