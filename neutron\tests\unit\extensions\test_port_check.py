#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock
from neutron_lib.api.definitions import portbindings
from neutron_lib import context
from neutron_lib import exceptions as n_exc
from neutron_lib.plugins import directory
from oslo_config import cfg
from oslo_utils import uuidutils

from neutron.conf.plugins.ml2.drivers import driver_type
from neutron.db import api as db_api
from neutron.db import provisioning_blocks as pb
from neutron.extensions import l3
from neutron.extensions import port_check as port_check_ext
from neutron.objects import ports as ports_obj
from neutron.plugins.ml2 import models as ml2_models
from neutron.services.port_check import plugin
from neutron.tests.unit.db import test_db_base_plugin_v2

_uuid = uuidutils.generate_uuid


class TestPortCheckPlugin(plugin.PortCheckPlugin):
    supported_extension_aliases = ['port-check']


class TestPortCheckExtensionManager(object):
    def get_resources(self):
        return (l3.L3.get_resources() +
                port_check_ext.Port_check.get_resources())

    def get_actions(self):
        return []

    def get_request_extensions(self):
        return []


class TestPortCheckExtension(
        test_db_base_plugin_v2.NeutronDbPluginV2TestCase):
    def setUp(self):
        cfg.CONF.set_override('mechanism_drivers',
                              ['logger', 'test'],
                              'ml2')
        driver_type.register_ml2_drivers_vlan_opts()
        cfg.CONF.set_override('network_vlan_ranges',
                              ['physnet1:1000:1099'],
                              group='ml2_type_vlan')

        svc_plugins = (
            'neutron.tests.unit.extensions.test_l3.'
            'TestL3NatAgentSchedulingServicePlugin',
            'neutron.tests.unit.extensions.test_port_check.'
            'TestPortCheckPlugin',
        )
        ext_mgr = TestPortCheckExtensionManager()
        super(TestPortCheckExtension, self).setUp(
            plugin='ml2',
            ext_mgr=ext_mgr, service_plugins=svc_plugins)
        self.plugin = directory.get_plugin()
        self.plugin.start_rpc_listeners()

        self.pc_plugin = directory.get_plugin('port-check')
        self.pc_plugin.ovs_agent_rpc = mock.Mock()

        self.ctx = context.get_admin_context()

    def test_port_check_without_port_and_host(self):
        self.assertRaises(n_exc.BadRequest,
                          self.pc_plugin.port_check,
                          self.ctx)

    def test_check_port_both_port_and_host(self):
        self.assertRaises(n_exc.BadRequest,
                          self.pc_plugin.port_check,
                          self.ctx, _uuid(), 'fake-host')

    def test_check_non_exist_port(self):
        self.assertRaises(n_exc.PortNotFound,
                          self.pc_plugin.port_check,
                          self.ctx, _uuid())

    def _create_port_with_binding(self, host,
                                  port_status=None,
                                  port_device_id='',
                                  port_device_owner='',
                                  binding_status=None,
                                  binding_vif_type=None):
        host_arg = {portbindings.HOST_ID: host,
                    'mac_address': 'aa:aa:aa:aa:aa:aa'}
        with self.port(name='name', device_id=port_device_id,
                       device_owner=port_device_owner,
                       arg_list=(portbindings.HOST_ID,),
                       **host_arg) as port:
            port_obj = ports_obj.Port.get_object(self.ctx,
                                                 id=port['port']['id'])
            if port_status:
                port_obj['status'] = port_status
                port_obj.update()
            binding = port_obj.bindings[0]
            if binding_status:
                binding['status'] = binding_status
            if binding_vif_type:
                binding['vif_type'] = binding_vif_type
            binding.update()
            return port

    def _mock_distributed_binding(self, port_id, router_id, host_id,
                                  vif_type=None, status='DOWN'):
        with db_api.context_manager.writer.using(self.ctx):
            record = ml2_models.DistributedPortBinding(
                port_id=port_id,
                host=host_id,
                router_id=router_id,
                vif_type=vif_type or portbindings.VIF_TYPE_UNBOUND,
                vnic_type=portbindings.VNIC_NORMAL,
                status=status)
            self.ctx.session.add(record)
            return record

    def test_check_one_normal_port(self):
        host = 'host-ovs-filter-active'
        port = self._create_port_with_binding(host)
        port_id = port['port']['id']
        with ((mock.patch.object(self.pc_plugin, '_get_agents',
                               return_value={host: 'l2_agent'}))):
            self.pc_plugin.ovs_agent_rpc.ports_check.return_value = \
                {port_id: {}}
            result = self.pc_plugin.port_check(self.ctx, port_id)
            self.assertEqual([], result['port_check']['binding'])
            self.assertEqual([], result['port_check']['provisioning'])
            self.assertEqual([], result['port_check']['status'])
            self.pc_plugin.ovs_agent_rpc.ports_check.assert_called()

    def test_check_one_port_with_status_down(self):
        port = self._create_port_with_binding('host-fail')
        self.assertEqual('DOWN', port['port']['status'])
        result = self.pc_plugin.port_check(self.ctx, port['port']['id'])
        self.assertEqual(['Port status is DOWN'],
                         result['port_check']['status'])

    def test_check_one_port_provisioning_blocks(self):
        with self.port() as port:
            port_id = port['port']['id']
            entity = 'L2'
            pb.add_provisioning_component(
                self.ctx, port_id, 'port', entity)
            result = self.pc_plugin.port_check(self.ctx, port_id)
            self.assertEqual(
                ['Port provisioning is not completed by %s agent' % entity],
                result['port_check']['provisioning'])

    def test_check_one_port_l2_inactive_binding(self):
        host = 'host-ovs-no_filter'
        port = self._create_port_with_binding(host, binding_status='INACTIVE')
        result = self.pc_plugin.port_check(self.ctx, port['port']['id'])
        self.assertEqual(['INACTIVE port binding on host %s' % host,
                          'ACTIVE port binding not found'],
                         result['port_check']['binding'])

    def test_check_one_port_unbound(self):
        host = 'host-ovs-no_filter'
        port = self._create_port_with_binding(host, binding_vif_type='unbound')
        result = self.pc_plugin.port_check(self.ctx, port['port']['id'])
        self.assertEqual(['Port is unbound'],
                         result['port_check']['binding'])

    def test_check_one_port_binding_failed(self):
        host = 'host-fail'
        port = self._create_port_with_binding(host)
        result = self.pc_plugin.port_check(self.ctx, port['port']['id'])
        self.assertEqual(['Port binding failed'],
                         result['port_check']['binding'])

    def test_check_one_port_with_check_dhcp(self):
        with self.port(name='name', device_owner='nova:fake') as port:
            port_id = port['port']['id']
            result = self.pc_plugin.port_check(self.ctx, port_id)
            self.assertNotIn('dhcp', result['port_check'])

        with self.port(name='name', device_owner='network:dhcp',
                       device_id='reserved_dhcp_port') as port:
            result = self.pc_plugin.port_check(self.ctx, port['port']['id'])
            self.assertEqual(
                ['%r device_id is unacceptable' % port['port']['device_id']],
                result['port_check']['dhcp'])

    def test_check_normal_dvr_port(self):
        host = 'host-ovs-filter-active'
        device_id = _uuid()
        device_owner = 'network:router_interface_distributed'
        port = self._create_port_with_binding(
            host, port_status='ACTIVE',
            port_device_id=device_id, port_device_owner=device_owner)
        port_id = port['port']['id']
        with (mock.patch.object(self.pc_plugin, '_get_agents',
                               return_value={host: 'agent'})):
            with mock.patch.object(self.pc_plugin.l3_plugin,
                                   '_get_router_ids_for_agent',
                                   return_value=[device_id]):
                self._mock_distributed_binding(port_id, device_id, host,
                                               vif_type='ovs',
                                               status='ACTIVE')
                self.pc_plugin.ovs_agent_rpc.ports_check.return_value = (
                    {port_id: {}})
                result = self.pc_plugin.port_check(self.ctx, port_id)
                result = result['port_check']
                self.assertEqual([], result['binding'])
                self.assertEqual([], result['provisioning'])
                self.assertEqual([], result['status'])
                (self.pc_plugin.ovs_agent_rpc.ports_check
                    .assert_called())

    def test_check_dvr_port_unbound(self):
        host = 'host-ovs-filter-active'
        device_id = _uuid()
        device_owner = 'network:router_interface_distributed'
        port = self._create_port_with_binding(
            host, port_device_id=device_id, port_device_owner=device_owner)
        port_id = port['port']['id']
        with mock.patch.object(self.pc_plugin, '_get_agents',
                               return_value={host: 'agent'}):
            with mock.patch.object(self.pc_plugin.l3_plugin,
                                   '_get_router_ids_for_agent',
                                   return_value=[device_id]):
                result = self.pc_plugin.port_check(self.ctx, port_id)
                result = result['port_check']
                self.assertEqual(
                    ['DVR port is not bound on host %s' % host],
                    result['binding'])

    def test_check_dvr_port_not_active(self):
        host = 'host-ovs-filter-active'
        device_id = _uuid()
        device_owner = 'network:router_interface_distributed'
        port = self._create_port_with_binding(
            host, port_device_id=device_id, port_device_owner=device_owner)
        port_id = port['port']['id']
        with (mock.patch.object(self.pc_plugin, '_get_agents',
                                return_value={host: 'agent'})):
            with mock.patch.object(self.pc_plugin.l3_plugin,
                                   '_get_router_ids_for_agent',
                                   return_value=[device_id]):
                self._mock_distributed_binding(port_id, device_id, host,
                                               vif_type='ovs')
                result = self.pc_plugin.port_check(self.ctx, port_id)
                result = result['port_check']
                self.assertEqual(
                    ['DVR port binding is %s on host %s' % ('DOWN', host)],
                    result['binding'])

    def test_check_ports_on_host(self):
        host = 'host-ovs-filter-active'
        port1 = self._create_port_with_binding(host)
        port2 = self._create_port_with_binding(host)
        result = self.pc_plugin.port_check(self.ctx, host_id=host)
        self.assertEqual([], result['port_check'][port1['port']['id']])
        self.assertEqual([], result['port_check'][port2['port']['id']])

    def test_check_ports_on_host_with_status_down(self):
        host = 'host-ovs-filter-active'
        port = self._create_port_with_binding(host, port_status='DOWN')
        self._create_port_with_binding(host)
        result = self.pc_plugin.port_check(self.ctx, host_id=host)
        self.assertEqual(['check status wrong'],
                         result['port_check'][port['port']['id']])
