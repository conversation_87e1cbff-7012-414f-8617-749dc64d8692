#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

from neutron_lib import constants
from neutron_lib import context
from oslo_config import cfg

from neutron.api import extensions
from neutron.db import l3_router_cloud_attributes_db
from neutron.extensions import cloud_attribute
from neutron.extensions import l3
from neutron.tests.unit.api import test_extensions
from neutron.tests.unit.db import test_db_base_plugin_v2
from neutron.tests.unit.extensions import test_l3


from neutron.db import agents_db
from neutron.db import db_base_plugin_v2
from neutron.extensions import agent
from neutron.tests.common import helpers


L3_HOSTA = 'hosta'


class CloudAttributesTestBaseCase(object):

    # 1. POST /v2.0/resources/{resource_id}
    # Body: {"resource": {"cloud_attributes": {"key1": "value1"}}}
    # Response: {"resource": {"cloud_attributes": {"key1": "value1"}}}
    def _create_resource_with_cloud_attributes(self):
        pass

    def _create_resource_with_cloud_attributes_error(self):
        pass

    def _test_create_resource_with_null_cloud_attributes(self):
        cloud_attributes = {}
        self._create_resource_with_cloud_attributes(cloud_attributes)

    def _test_create_resource_with_one_key_value(self):
        cloud_attributes = {'a': '1'}
        self._create_resource_with_cloud_attributes(cloud_attributes)

    def _test_create_resource_with_multi_key_value(self):
        cloud_attributes = {'a': '1', 'n': '2', 'c': '3'}
        self._create_resource_with_cloud_attributes(cloud_attributes)

    def _test_create_resource_with_complex_key_value(self):
        cloud_attributes = {'pi': 3.141592653589793,
                            'str': 'abcdefg',
                            'list': ['apple', 'banana', 'cherry', 3, True,
                                     {'color': 'red', 'animal': 'dog'}],
                            'dict': {'person':
                                     {'age': 28,
                                      'city': 'Beijing'},
                                     'inventory':
                                     {'apple': [10, 15],
                                      'banana': [5, 10]}
                                     },
                            'none': None,
                            'bool': True}
        self._create_resource_with_cloud_attributes(cloud_attributes)

    def _test_create_resource_with_repeated_key_and_value(self):
        cloud_attributes = {'a': '1',
                            'a': '1',
                            'b': '2',
                            'b': '2',
                            'c': '3',
                            'c': '3'}
        exp_cloud_attr = {'a': '1',
                          'b': '2',
                          'c': '3'}
        self._create_resource_with_cloud_attributes(
            cloud_attributes, exp_cloud_attr)

    def _test_create_resource_with_repeated_key_but_different_value(self):
        cloud_attributes = {'a': '1',
                            'a': '10',
                            'b': '2',
                            'b': '20',
                            'c': '3',
                            'c': '30'}
        exp_cloud_attr = {'a': '10',
                          'b': '20',
                          'c': '30'}
        self._create_resource_with_cloud_attributes(
            cloud_attributes, exp_cloud_attr)

    def _test_create_resource_with_cloud_attributes_exceeding_limit(self):
        cloud_attributes = {'fruit' * 500: 'apple' * 500,
                            'animal' * 500: 'dog' * 500,
                            'color' * 500: 'red' * 500}
        status_int = 400
        self._create_resource_with_cloud_attributes_error(
            cloud_attributes, status_int)

    def _test_create_resource_with_invalid_type_of_cloud_attributes(self):
        status_int = 400
        cloud_attributes = 'a'
        self._create_resource_with_cloud_attributes_error(
            cloud_attributes, status_int)
        cloud_attributes = ['a', 'b', 'c']
        self._create_resource_with_cloud_attributes_error(
            cloud_attributes, status_int)
        cloud_attributes = True
        self._create_resource_with_cloud_attributes_error(
            cloud_attributes, status_int)

    def _test_create_resource_with_cloud_attributes(self):
        self._test_create_resource_with_one_key_value()
        self._test_create_resource_with_multi_key_value()
        # Clear the existing cloud_attributes for the agent in 'hosta'
        self._test_remove_all_cloud_attributes()
        self._test_create_resource_with_complex_key_value()
        # Clear the existing cloud_attributes for the agent in 'hosta'
        self._test_remove_all_cloud_attributes()
        self._test_create_resource_with_repeated_key_and_value()
        self._test_create_resource_with_repeated_key_but_different_value()
        self._test_create_resource_with_cloud_attributes_exceeding_limit()
        self._test_create_resource_with_invalid_type_of_cloud_attributes()

    # 2. GET /v2.0/resources/{resource_id}/cloud_attributes
    # Body: None
    # Response: {"cloud_attributes": {"key1": "value1"}}
    def _get_cloud_attributes(self):
        pass

    def _test_get_cloud_attributes(self):
        self._get_cloud_attributes()

    # 3. GET /v2.0/resources/{resource_id}/cloud_attributes/key
    # Body: None
    # Response: {"key": "value"}
    def _get_cloud_attributes_by_key(self):
        pass

    def _test_get_cloud_attributes_by_key(self):
        self._get_cloud_attributes()

    # 4. PUT /v2.0/resources/{resource_id}
    # Body: {"resource": {"cloud_attributes": {"key1": "value1"}}}
    # Response: {"resource": {"cloud_attributes": {"key1": "value1"}}}
    def _update_resource_with_cloud_attributes(self):
        pass

    def _update_resource_with_cloud_attributes_error(self):
        pass

    def _test_update_resource_from_null_to_one_key_value(self):
        cloud_attributes = {}
        update_cloud_attr = {'a': '1'}
        exp_cloud_attr = {'a': '1'}
        self._update_resource_with_cloud_attributes(
            cloud_attributes, update_cloud_attr, exp_cloud_attr)

    def _test_update_resource_to_multi_key_value(self):
        cloud_attributes = {'a': '1', 'b': '2', 'c': '3'}
        update_cloud_attr = {'a': '1', 'b': '2', 'd': '4'}
        exp_cloud_attr = {'a': '1', 'b': '2', 'c': '3', 'd': '4'}
        self._update_resource_with_cloud_attributes(
            cloud_attributes, update_cloud_attr, exp_cloud_attr)

    def _test_update_resource_to_complex_key_value(self):
        cloud_attributes = {'a': '1', 'b': '2', 'c': '3'}
        update_cloud_attr = {'pi': 3.141592653589793,
                             'str': 'abcdefg',
                             'list': ['apple', 'banana', 'cherry', 3, True,
                                      {'color': 'red', 'animal': 'dog'}],
                             'dict': {'person':
                                      {'age': 28,
                                       'city': 'Beijing'},
                                      'inventory':
                                      {'apple': [10, 15],
                                       'banana': [5, 10]}
                                      },
                             'none': None,
                             'bool': True}
        exp_cloud_attr = {'a': '1',
                          'b': '2',
                          'c': '3',
                          'pi': 3.141592653589793,
                          'str': 'abcdefg',
                          'list': ['apple', 'banana', 'cherry', 3, True,
                                   {'color': 'red', 'animal': 'dog'}],
                          'dict': {'person':
                                   {'age': 28,
                                    'city': 'Beijing'},
                                   'inventory':
                                   {'apple': [10, 15],
                                    'banana': [5, 10]}
                                   },
                          'none': None,
                          'bool': True}
        self._update_resource_with_cloud_attributes(
            cloud_attributes, update_cloud_attr, exp_cloud_attr)

    def _test_update_resource_to_cloud_attributes_exceeding_length_limit(self):
        cloud_attributes = {'fruit': 'apple', 'animal': 'dog', 'color': 'red'}
        update_cloud_attr = {'fruit': 'apple' * 1000,
                             'animal': 'dog' * 1000,
                             'color': 'red' * 1000}
        expected_code = 400
        self._update_resource_with_cloud_attributes_error(
            cloud_attributes, update_cloud_attr, expected_code)

    def _test_update_resource_to_invalid_type_of_cloud_attributes(self):
        expected_code = 400
        cloud_attributes = {'a': '1', 'b': '2', 'c': '3'}
        update_cloud_attr = 'a'
        self._update_resource_with_cloud_attributes_error(
            cloud_attributes, update_cloud_attr, expected_code)

        update_cloud_attr = ['a', 'b', 'c']
        self._update_resource_with_cloud_attributes_error(
            cloud_attributes, update_cloud_attr, expected_code)

    def _test_update_resource_with_cloud_attributes(self):
        self._test_update_resource_from_null_to_one_key_value()
        self._test_update_resource_to_multi_key_value()
        # Clear the existing cloud_attributes for the agent in 'hosta'
        self._test_remove_all_cloud_attributes()
        self._test_update_resource_to_complex_key_value()
        self._test_update_resource_to_cloud_attributes_exceeding_length_limit()
        self._test_update_resource_to_invalid_type_of_cloud_attributes()

    # 5. PUT /v2.0/resources/{resource_id}/cloud_attributes
    # Before update: {"cloud_attributes": {"a": ["1"]}}
    # Body: {"cloud_attributes": {"a": "2"}}
    # Response: {"cloud_attributes": {"a": ["1", "2"]}}
    # Extend the cloud_attributes. If the target key exists, the values of
    # the original cloud_attributes to be extend should be of type list
    def _extend_cloud_attributes(self):
        pass

    def _extend_cloud_attributes_error(self):
        pass

    def _test_extend_cloud_attributes_with_single_value(self):
        cloud_attributes = {'a': ['1', '2'],
                            'b': ['4', '5']}
        ext_cloud_attr = {'cloud_attributes': {'a': '3',
                                               'b': '6'}}
        exp_cloud_attr = {'a': ['1', '2', '3'],
                          'b': ['4', '5', '6']}
        self._extend_cloud_attributes(cloud_attributes, ext_cloud_attr,
                                      exp_cloud_attr)

    def _test_extend_cloud_attributes_with_list_values(self):
        cloud_attributes = {'a': ['1', '2'],
                            'b': ['5', '6']}
        ext_cloud_attr = {'cloud_attributes': {'a': ['3', '4'],
                                               'b': ['7', '8']}}
        exp_cloud_attr = {'a': ['1', '2', '3', '4'],
                          'b': ['5', '6', '7', '8']}
        self._extend_cloud_attributes(cloud_attributes, ext_cloud_attr,
                                      exp_cloud_attr)

    def _test_extend_cloud_attributes_with_partial_repeated_list_values(self):
        cloud_attributes = {'a': ['1', '2'],
                            'b': ['4', '5']}
        ext_cloud_attr = {'cloud_attributes': {'a': ['1', '3'],
                                               'b': ['4', '6']}}
        exp_cloud_attr = {'a': ['1', '2', '3'],
                          'b': ['4', '5', '6']}
        self._extend_cloud_attributes(cloud_attributes, ext_cloud_attr,
                                      exp_cloud_attr)

    def _test_extend_cloud_attributes_with_non_exist_keys(self):
        cloud_attributes = {'a': ['1', '2']}
        ext_cloud_attr = {'cloud_attributes': {'b': '3',
                                               'c': ['4', '5']}}
        exp_cloud_attr = {'a': ['1', '2'],
                          'b': '3',
                          'c': ['4', '5']}
        self._extend_cloud_attributes(cloud_attributes, ext_cloud_attr,
                                      exp_cloud_attr)

    def _test_extend_cloud_attributes_with_original_values_not_list(self):
        cloud_attributes = {'a': '1'}
        ext_cloud_attr = {'cloud_attributes': {'a': ['1', '2']}}
        status_int = 400
        self._extend_cloud_attributes_error(cloud_attributes, ext_cloud_attr,
                                            status_int)

    def _test_extend_cloud_attributes_exceeding_length_limit(self):
        cloud_attributes = {}
        ext_cloud_attr = {'fruit': 'apple' * 1000,
                          'animal': 'dog' * 1000,
                          'color': 'red' * 1000}
        status_int = 400
        self._extend_cloud_attributes_error(cloud_attributes, ext_cloud_attr,
                                            status_int)

    def _test_extend_cloud_attributes(self):
        self._test_extend_cloud_attributes_with_single_value()
        self._test_extend_cloud_attributes_with_list_values()
        self._test_extend_cloud_attributes_with_partial_repeated_list_values()
        # Clear the existing cloud_attributes for the agent in 'hosta'
        self._test_remove_all_cloud_attributes()
        self._test_extend_cloud_attributes_with_non_exist_keys()
        self._test_extend_cloud_attributes_with_original_values_not_list()
        self._test_extend_cloud_attributes_exceeding_length_limit()

    # 6. DELETE /v2.0/resources/{resource_id}/cloud_attributes/key
    # Before delete: {"cloud_attributes": {"a": "1", "key", "2"}}
    # Body: None
    # Response: None
    # After delete: {"cloud_attributes": {"a": "1"}}
    def _remove_cloud_attributes_by_key(self):
        pass

    def _remove_cloud_attributes_by_key_error(self):
        pass

    def _test_remove_cloud_attributes_by_exist_key(self):
        cloud_attributes = {'a': '1', 'b': '2', 'c': '3'}
        key_to_delete = 'b'
        exp_cloud_attr = {'a': '1', 'c': '3'}
        self._remove_cloud_attributes_by_key(cloud_attributes, key_to_delete,
                                             exp_cloud_attr)

    def _test_remove_cloud_attributes_by_non_exist_key(self):
        cloud_attributes = {'a': '1', 'b': '2', 'c': '3'}
        key_to_delete = 'd'
        self._remove_cloud_attributes_by_key_error(cloud_attributes,
                                                   key_to_delete)

    def _test_remove_cloud_attributes_by_key(self):
        self._test_remove_cloud_attributes_by_exist_key()
        self._test_remove_cloud_attributes_by_non_exist_key()

    # 7. DELETE /v2.0/resources/{resource_id}/cloud_attributes
    # Before delete: {"cloud_attributes": {"a": ["1", "2"]}}
    # Body: {"cloud_attributes": {"a": "2"}} or {"cloud_attributes": ["a"]}
    # Response: {"cloud_attributes": {"a": ["1"]}} or {"cloud_attributes": {}}
    # Partial deletion of the cloud_attributes.
    def _remove_cloud_attributes(self):
        pass

    def _remove_cloud_attributes_error(self):
        pass

    def _remove_all_cloud_attributes(self):
        pass

    def _test_remove_cloud_attributes_by_keys(self):
        cloud_attributes = {'a': '1', 'b': '2', 'c': '3'}
        del_cloud_attr = {'cloud_attributes': ['a', 'b']}
        exp_cloud_attr = {'c': '3'}
        self._remove_cloud_attributes(cloud_attributes, del_cloud_attr,
                                      exp_cloud_attr)

    def _test_remove_cloud_attributes_by_non_exist_keys(self):
        cloud_attributes = {'a': '1', 'b': '2', 'c': '3'}
        del_cloud_attr = {'cloud_attributes': ['c', 'd']}
        status_int = 400
        self._remove_cloud_attributes_error(cloud_attributes, del_cloud_attr,
                                            status_int)

    def _test_remove_cloud_attributes_by_dicts(self):
        cloud_attributes = {'key1': ['a', 'b', 'c'],
                            'key2': [{'d': '1'}, {'e': '2'}, {'f': '3'}]}
        del_cloud_attr = {'cloud_attributes':
                          {'key1': 'b',
                           'key2': {'e': '2'}}}
        exp_cloud_attr = {'key1': ['a', 'c'],
                          'key2': [{'d': '1'}, {'f': '3'}]}
        self._remove_cloud_attributes(cloud_attributes, del_cloud_attr,
                                      exp_cloud_attr)
        cloud_attributes = {'key1': ['a', 'b', 'c'],
                            'key2': [{'d': '1'}, {'e': '2'}, {'f': '3'}]}
        del_cloud_attr = {'cloud_attributes':
                          {'key1': ['b', 'c'],
                           'key2': [{'e': '2'}, {'f': '3'}]}}
        exp_cloud_attr = {'key1': ['a'],
                          'key2': [{'d': '1'}]}
        self._remove_cloud_attributes(cloud_attributes, del_cloud_attr,
                                      exp_cloud_attr)
        cloud_attributes = {'key1': 'a',
                            'key2': {'b': 2},
                            'key3': ['c', 'd']}
        del_cloud_attr = {'cloud_attributes':
                          {'key1': 'a',
                           'key2': {'b': 2},
                           'key3': ['c', 'd']}}
        exp_cloud_attr = {}
        self._remove_cloud_attributes(cloud_attributes, del_cloud_attr,
                                      exp_cloud_attr)

    def _test_remove_cloud_attributes_by_dicts_with_non_exist_values(self):
        status_int = 400
        cloud_attributes = {'key1': 'a'}
        del_cloud_attr = {'cloud_attributes': {'key1': 'b'}}
        self._remove_cloud_attributes_error(cloud_attributes, del_cloud_attr,
                                            status_int)
        cloud_attributes = {'key2': {'b': 2}}
        del_cloud_attr = {'cloud_attributes': {'key2': {'b': 3}}}
        self._remove_cloud_attributes_error(cloud_attributes, del_cloud_attr,
                                            status_int)
        cloud_attributes = {'key1': ['a', 'b', 'c'],
                            'key2': [{'d': '1'}, {'e': '2'}, {'f': '3'}]}
        del_cloud_attr = {'cloud_attributes': {'key1': 'd'}}
        self._remove_cloud_attributes_error(cloud_attributes, del_cloud_attr,
                                            status_int)
        cloud_attributes = {'key1': ['a', 'b', 'c'],
                            'key2': [{'d': '1'}, {'e': '2'}, {'f': '3'}]}
        del_cloud_attr = {'cloud_attributes': {'key2': {'f': '4'}}}
        self._remove_cloud_attributes_error(cloud_attributes, del_cloud_attr,
                                            status_int)

    def _test_remove_all_cloud_attributes(self):
        cloud_attributes = {'pi': 3.141592653589793,
                            'str': 'abcdefg',
                            'list': ['apple', 'banana', 'cherry', 3, True,
                                     {'color': 'red', 'animal': 'dog'}],
                            'dict': {'person':
                                     {'age': 28,
                                      'city': 'Beijing'},
                                     'inventory':
                                     {'apple': [10, 15],
                                      'banana': [5, 10]}
                                     },
                            'none': None,
                            'bool': True}
        self._remove_all_cloud_attributes(cloud_attributes)

    def _test_remove_cloud_attributes(self):
        self._test_remove_cloud_attributes_by_keys()
        self._test_remove_cloud_attributes_by_non_exist_keys()
        # Clear the existing cloud_attributes for the agent in 'hosta'
        self._test_remove_all_cloud_attributes()
        self._test_remove_cloud_attributes_by_dicts()
        self._test_remove_cloud_attributes_by_dicts_with_non_exist_values()


class RouterCloudAttributesTestExtensionManager(object):

    def get_resources(self):
        return (l3.L3.get_resources() +
                cloud_attribute.Cloud_attribute.get_resources())

    def get_actions(self):
        return []

    def get_request_extensions(self):
        return []


class RouterCloudAttributesTestPlugin(
    test_l3.TestL3NatIntPlugin,
    l3_router_cloud_attributes_db.RouterCloudAttributesMixin):
    supported_extension_aliases = ["router"]


class RouterCloudAttributesTestCase(test_l3.L3BaseForIntTests,
                                    test_l3.L3NatTestCaseMixin,
                                    CloudAttributesTestBaseCase):

    def setUp(self):
        plugin = ('neutron.tests.unit.extensions.'
                  'test_cloud_attribute.'
                  'RouterCloudAttributesTestPlugin')
        svc_plugins = ('neutron.services.cloud_attribute.plugin.'
                       'CloudAttributePlugin',)
        ext_mgr = RouterCloudAttributesTestExtensionManager()
        super(RouterCloudAttributesTestCase, self).setUp(
            plugin=plugin, ext_mgr=ext_mgr, service_plugins=svc_plugins)

    # 1. POST /v2.0/resources/{resource_id}
    def _create_resource_with_cloud_attributes(self, cloud_attributes,
                                               exp_cloud_attr=None):
        with self.router(cloud_attributes=cloud_attributes) as router:
            if exp_cloud_attr:
                self.assertEqual(exp_cloud_attr,
                                 router['router']['cloud_attributes'])
            else:
                self.assertEqual(cloud_attributes,
                                 router['router']['cloud_attributes'])

    def _create_resource_with_cloud_attributes_error(self, cloud_attributes,
                                                     status_int):
        res = self._create_router(self.fmt, 'tenant_id',
                                  cloud_attributes=cloud_attributes)
        self.assertEqual(status_int, res.status_int)

    def test_create_resource_without_cloud_attributes(self):
        with self.router() as router:
            self.assertEqual({}, router['router']['cloud_attributes'])

    def test_create_resource_with_cloud_attributes(self):
        self._test_create_resource_with_cloud_attributes()

    # 2. GET /v2.0/resources/{resource_id}/cloud_attributes
    def _get_cloud_attributes(self):
        cloud_attributes = {'a': '1', 'n': '2', 'c': '3'}
        with self.router(cloud_attributes=cloud_attributes) as router:
            req = self.new_show_request("routers", router['router']['id'],
                                        subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            res = self.deserialize(self.fmt, res)
            self.assertEqual(cloud_attributes, res['cloud_attributes'])

    def test_get_cloud_attributes(self):
        self._test_get_cloud_attributes()

    # 3. GET /v2.0/resources/{resource_id}/cloud_attributes/key
    def _get_cloud_attributes_by_key(self):
        cloud_attributes = {'a': '1', 'b': '2', 'c': '3'}
        key = 'b'
        exp_cloud_attr = {'b': '2'}
        with self.router(cloud_attributes=cloud_attributes) as router:
            req = self.new_show_request("routers", router['router']['id'],
                                        subresource='cloud_attributes',
                                        sub_id=key)
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            res = self.deserialize(self.fmt, res)
            self.assertEqual(exp_cloud_attr, res)

    def test_get_cloud_attributes_by_key(self):
        self._test_get_cloud_attributes_by_key()

    # 4. PUT /v2.0/resources/{resource_id}
    def _update_resource_with_cloud_attributes(
            self, cloud_attributes, update_cloud_attr, exp_cloud_attr):
        with self.router(cloud_attributes=cloud_attributes) as router:
            res = self._update(
                'routers', router['router']['id'],
                {'router': {'cloud_attributes': update_cloud_attr}})
            self.assertEqual(exp_cloud_attr, res['router']['cloud_attributes'])

    def _update_resource_with_cloud_attributes_error(
            self, cloud_attributes, update_cloud_attr, expected_code):
        with self.router(cloud_attributes=cloud_attributes) as router:
            self._update(
                'routers', router['router']['id'],
                {'router': {'cloud_attributes': update_cloud_attr}},
                expected_code)

    def test_update_resource_with_cloud_attributes(self):
        self._test_update_resource_with_cloud_attributes()

    # 5. PUT /v2.0/resources/{resource_id}/cloud_attributes
    def _extend_cloud_attributes(self, cloud_attributes, ext_cloud_attr,
                                 exp_cloud_attr):
        with self.router(cloud_attributes=cloud_attributes) as router:
            req = self.new_update_request("routers",
                                          data=ext_cloud_attr,
                                          id=router['router']['id'],
                                          subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            res = self.deserialize(self.fmt, res)
            self.assertEqual(exp_cloud_attr, res['cloud_attributes'])

    def _extend_cloud_attributes_error(self, cloud_attributes, ext_cloud_attr,
                                       status_int):
        with self.router(cloud_attributes=cloud_attributes) as router:
            req = self.new_update_request("routers",
                                          data=ext_cloud_attr,
                                          id=router['router']['id'],
                                          subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            self.assertEqual(status_int, res.status_int)

    def test_extend_cloud_attributes(self):
        self._test_extend_cloud_attributes()

    # 6. DELETE /v2.0/resources/{resource_id}/cloud_attributes/key
    def _remove_cloud_attributes_by_key(self, cloud_attributes, key_to_delete,
                                        exp_cloud_attr):
        with self.router(cloud_attributes=cloud_attributes) as router:
            req = self.new_delete_request("routers",
                                          router['router']['id'],
                                          subresource='cloud_attributes',
                                          sub_id=key_to_delete)
            req.get_response(self._api_for_resource("cloud_attributes"))
            req = self.new_show_request("routers", router['router']['id'],
                                        subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            res = self.deserialize(self.fmt, res)
            self.assertEqual(exp_cloud_attr, res['cloud_attributes'])

    def _remove_cloud_attributes_by_key_error(self, cloud_attributes,
                                              key_to_delete):
        with self.router(cloud_attributes=cloud_attributes) as router:
            req = self.new_delete_request("routers",
                                          router['router']['id'],
                                          subresource='cloud_attributes',
                                          sub_id=key_to_delete)
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            self.assertEqual(400, res.status_int)

    def test_remove_cloud_attributes_by_key(self):
        self._test_remove_cloud_attributes_by_key()

    # 7. DELETE /v2.0/resources/{resource_id}/cloud_attributes
    def _remove_cloud_attributes(self, cloud_attributes, del_cloud_attr,
                                 exp_cloud_attr):
        with self.router(cloud_attributes=cloud_attributes) as router:
            req = self.new_delete_request("routers",
                                          router['router']['id'],
                                          data=del_cloud_attr,
                                          subresource='cloud_attributes')
            req.get_response(self._api_for_resource("cloud_attributes"))
            req = self.new_show_request("routers", router['router']['id'],
                                        subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            res = self.deserialize(self.fmt, res)
            self.assertEqual(exp_cloud_attr, res['cloud_attributes'])

    def _remove_cloud_attributes_error(self, cloud_attributes, del_cloud_attr,
                                       status_int):
        with self.router(cloud_attributes=cloud_attributes) as router:
            req = self.new_delete_request("routers",
                                          router['router']['id'],
                                          data=del_cloud_attr,
                                          subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            self.assertEqual(status_int, res.status_int)

    def _remove_all_cloud_attributes(self, cloud_attributes):
        with self.router(cloud_attributes=cloud_attributes) as router:
            req = self.new_delete_request("routers",
                                          router['router']['id'],
                                          subresource='cloud_attributes')
            req.get_response(self._api_for_resource("cloud_attributes"))
            req = self.new_show_request("routers", router['router']['id'],
                                        subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            res = self.deserialize(self.fmt, res)
            self.assertEqual({}, res['cloud_attributes'])

    def test_remove_cloud_attributes(self):
        self._test_remove_cloud_attributes()


class NetworkCloudAttributesTestCase(
    test_db_base_plugin_v2.NeutronDbPluginV2TestCase,
    CloudAttributesTestBaseCase):
    """Test API extension network_cloud_attributes attributes.
    """

    def setUp(self):
        plugin = ('neutron.plugins.ml2.plugin.Ml2Plugin')
        svc_plugins = ('neutron.services.cloud_attribute.plugin.'
                       'CloudAttributePlugin',)
        super(NetworkCloudAttributesTestCase, self).setUp(
            plugin=plugin, service_plugins=svc_plugins)
        ext_mgr = extensions.PluginAwareExtensionManager.get_instance()
        self.ext_api = test_extensions.setup_extensions_middleware(ext_mgr)

    # 1. POST /v2.0/resources/{resource_id}
    def _create_resource_with_cloud_attributes(
            self, cloud_attributes, exp_cloud_attr=None):
        with self.network(cloud_attributes=cloud_attributes) as network:
            if exp_cloud_attr:
                self.assertEqual(exp_cloud_attr,
                                network['network']['cloud_attributes'])
            else:
                self.assertEqual(cloud_attributes,
                                network['network']['cloud_attributes'])

    def _create_resource_with_cloud_attributes_error(
            self, cloud_attributes, status_int):
        res = self._create_network(self.fmt, 'some_net', True,
                                   cloud_attributes=cloud_attributes)
        self.assertEqual(status_int, res.status_int)

    def test_create_resource_without_cloud_attributes(self):
        with self.network() as network:
            self.assertEqual({}, network['network']['cloud_attributes'])

    def test_create_resource_with_cloud_attributes(self):
        self._test_create_resource_with_cloud_attributes()

    # 2. GET /v2.0/resources/{resource_id}/cloud_attributes
    def _get_cloud_attributes(self):
        cloud_attributes = {'a': '1', 'n': '2', 'c': '3'}
        with self.network(cloud_attributes=cloud_attributes) as network:
            req = self.new_show_request("networks", network['network']['id'],
                                        subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            res = self.deserialize(self.fmt, res)
            self.assertEqual(cloud_attributes, res['cloud_attributes'])

    def test_get_cloud_attributes(self):
        self._test_get_cloud_attributes()

    # 3. GET /v2.0/resources/{resource_id}/cloud_attributes/key
    def _get_cloud_attributes_by_key(self):
        cloud_attributes = {'a': '1', 'b': '2', 'c': '3'}
        key = 'b'
        exp_cloud_attr = {'b': '2'}
        with self.network(cloud_attributes=cloud_attributes) as network:
            req = self.new_show_request("networks", network['network']['id'],
                                        subresource='cloud_attributes',
                                        sub_id=key)
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            res = self.deserialize(self.fmt, res)
            self.assertEqual(exp_cloud_attr, res)

    def test_get_cloud_attributes_by_key(self):
        self._test_get_cloud_attributes_by_key()

    # 4. PUT /v2.0/resources/{resource_id}
    def _update_resource_with_cloud_attributes(
            self, cloud_attributes, update_cloud_attr, exp_cloud_attr):
        with self.network(cloud_attributes=cloud_attributes) as network:
            res = self._update(
                'networks', network['network']['id'],
                {'network': {'cloud_attributes': update_cloud_attr}})
            self.assertEqual(exp_cloud_attr,
                             res['network']['cloud_attributes'])

    def _update_resource_with_cloud_attributes_error(
            self, cloud_attributes, update_cloud_attr, expected_code):
        with self.network(cloud_attributes=cloud_attributes) as network:
            self._update(
                'networks', network['network']['id'],
                {'network': {'cloud_attributes': update_cloud_attr}},
                expected_code)

    def test_update_resource_with_cloud_attributes(self):
        self._test_update_resource_with_cloud_attributes()

    # 5. PUT /v2.0/resources/{resource_id}/cloud_attributes
    def _extend_cloud_attributes(self, cloud_attributes, ext_cloud_attr,
                                 exp_cloud_attr):
        with self.network(cloud_attributes=cloud_attributes) as network:
            req = self.new_update_request("networks",
                                          data=ext_cloud_attr,
                                          id=network['network']['id'],
                                          subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            res = self.deserialize(self.fmt, res)
            self.assertEqual(exp_cloud_attr, res['cloud_attributes'])

    def _extend_cloud_attributes_error(self, cloud_attributes, ext_cloud_attr,
                                       status_int):
        with self.network(cloud_attributes=cloud_attributes) as network:
            req = self.new_update_request("networks",
                                          data=ext_cloud_attr,
                                          id=network['network']['id'],
                                          subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            self.assertEqual(status_int, res.status_int)

    def test_extend_cloud_attributes(self):
        self._test_extend_cloud_attributes()

    # 6. DELETE /v2.0/resources/{resource_id}/cloud_attributes/key
    def _remove_cloud_attributes_by_key(self, cloud_attributes,
                                        key_to_delete, exp_cloud_attr):
        with self.network(cloud_attributes=cloud_attributes) as network:
            req = self.new_delete_request("networks",
                                          network['network']['id'],
                                          subresource='cloud_attributes',
                                          sub_id=key_to_delete)
            req.get_response(self._api_for_resource("cloud_attributes"))
            req = self.new_show_request("networks", network['network']['id'],
                                        subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            res = self.deserialize(self.fmt, res)
            self.assertEqual(exp_cloud_attr, res['cloud_attributes'])

    def _remove_cloud_attributes_by_key_error(self, cloud_attributes,
                                              key_to_delete):
        with self.network(cloud_attributes=cloud_attributes) as network:
            req = self.new_delete_request("networks",
                                          network['network']['id'],
                                          subresource='cloud_attributes',
                                          sub_id=key_to_delete)
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            self.assertEqual(400, res.status_int)

    def test_remove_cloud_attributes_by_key(self):
        self._test_remove_cloud_attributes_by_key()

    # 7. DELETE /v2.0/resources/{resource_id}/cloud_attributes
    def _remove_cloud_attributes(self, cloud_attributes, del_cloud_attr,
                                 exp_cloud_attr):
        with self.network(cloud_attributes=cloud_attributes) as network:
            req = self.new_delete_request("networks",
                                          network['network']['id'],
                                          data=del_cloud_attr,
                                          subresource='cloud_attributes')
            req.get_response(self._api_for_resource("cloud_attributes"))
            req = self.new_show_request("networks", network['network']['id'],
                                        subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            res = self.deserialize(self.fmt, res)
            self.assertEqual(exp_cloud_attr, res['cloud_attributes'])

    def _remove_cloud_attributes_error(self, cloud_attributes,
                                       del_cloud_attr,
                                       status_int):
        with self.network(cloud_attributes=cloud_attributes) as network:
            req = self.new_delete_request("networks",
                                          network['network']['id'],
                                          data=del_cloud_attr,
                                          subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            self.assertEqual(status_int, res.status_int)

    def _remove_all_cloud_attributes(self, cloud_attributes):
        with self.network(cloud_attributes=cloud_attributes) as network:
            req = self.new_delete_request("networks",
                                          network['network']['id'],
                                          subresource='cloud_attributes')
            req.get_response(self._api_for_resource("cloud_attributes"))
            req = self.new_show_request("networks", network['network']['id'],
                                        subresource='cloud_attributes')
            res = req.get_response(self._api_for_resource("cloud_attributes"))
            res = self.deserialize(self.fmt, res)
            self.assertEqual({}, res['cloud_attributes'])

    def test_remove_cloud_attributes(self):
        self._test_remove_cloud_attributes()


class SubnetCloudAttributesTestCase(
         test_db_base_plugin_v2.NeutronDbPluginV2TestCase,
         CloudAttributesTestBaseCase):
    """Test API extension subnet_cloud_attributes attributes.
    """
    CIDRS = '10.0.0.0/8'
    IP_VERSION = 4

    def setUp(self):
        plugin = ('neutron.plugins.ml2.plugin.Ml2Plugin')
        svc_plugins = ('neutron.services.cloud_attribute.plugin.'
                       'CloudAttributePlugin',)
        super(SubnetCloudAttributesTestCase, self).setUp(
            plugin=plugin, service_plugins=svc_plugins)
        ext_mgr = extensions.PluginAwareExtensionManager.get_instance()
        self.ext_api = test_extensions.setup_extensions_middleware(ext_mgr)

    # 1. POST /v2.0/resources/{resource_id}
    def _create_subnet_with_cloud_attributes(
            self, cloud_attributes=None, cidr=None, network=None,
            enable_dhcp=False):
        if not network:
            with self.network() as network:
                pass
        network = network['network']
        if not cidr:
            cidr = self.CIDRS
        args = {'net_id': network['id'],
                'tenant_id': network['tenant_id'],
                'cidr': cidr,
                'ip_version': self.IP_VERSION,
                'enable_dhcp': enable_dhcp}
        args['cloud_attributes'] = cloud_attributes
        return self._create_subnet(self.fmt, **args)

    def _create_resource_with_cloud_attributes(
            self, cloud_attributes=None, exp_cloud_attr=None):
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        subnet = self.deserialize(self.fmt, res)
        subnet = subnet['subnet']
        if exp_cloud_attr:
            self.assertEqual(exp_cloud_attr,
                             subnet['cloud_attributes'])
        else:
            if not cloud_attributes:
                cloud_attributes = {}
            self.assertEqual(cloud_attributes,
                             subnet['cloud_attributes'])

    def _create_resource_with_cloud_attributes_error(
            self, cloud_attributes, status_int):
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        self.assertEqual(status_int, res.status_int)

    def test_create_resource_without_cloud_attributes(self):
        res = self._create_subnet_with_cloud_attributes()
        subnet = self.deserialize(self.fmt, res)
        self.assertEqual({}, subnet['subnet']['cloud_attributes'])

    def test_create_resource_with_cloud_attributes(self):
        self._test_create_resource_with_cloud_attributes()

    # 2. GET /v2.0/resources/{resource_id}/cloud_attributes
    def _get_cloud_attributes(self):
        cloud_attributes = {'a': '1', 'n': '2', 'c': '3'}
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        subnet = self.deserialize(self.fmt, res)
        req = self.new_show_request("subnets", subnet['subnet']['id'],
                                    subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        res = self.deserialize(self.fmt, res)
        self.assertEqual(cloud_attributes, res['cloud_attributes'])

    def test_get_cloud_attributes(self):
        self._test_get_cloud_attributes()

    # 3. GET /v2.0/resources/{resource_id}/cloud_attributes/key
    def _get_cloud_attributes_by_key(self):
        cloud_attributes = {'a': '1', 'b': '2', 'c': '3'}
        key = 'b'
        exp_cloud_attr = {'b': '2'}
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        subnet = self.deserialize(self.fmt, res)
        req = self.new_show_request("subnets", subnet['subnet']['id'],
                                    subresource='cloud_attributes',
                                    sub_id=key)
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        res = self.deserialize(self.fmt, res)
        self.assertEqual(exp_cloud_attr, res)

    def test_get_cloud_attributes_by_key(self):
        self._test_get_cloud_attributes_by_key()

    # 4. PUT /v2.0/resources/{resource_id}
    def _update_resource_with_cloud_attributes(
            self, cloud_attributes, update_cloud_attr, exp_cloud_attr):
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        subnet = self.deserialize(self.fmt, res)
        data = {'subnet': {'cloud_attributes': update_cloud_attr}}
        req = self.new_update_request('subnets', data, subnet['subnet']['id'])
        res = req.get_response(self.api)
        res = self.deserialize(self.fmt, res)
        self.assertEqual(exp_cloud_attr, res['subnet']['cloud_attributes'])

    def _update_resource_with_cloud_attributes_error(
            self, cloud_attributes, update_cloud_attr, status_int):
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        subnet = self.deserialize(self.fmt, res)
        data = {'subnet': {'cloud_attributes': update_cloud_attr}}
        req = self.new_update_request('subnets', data, subnet['subnet']['id'])
        res = req.get_response(self.api)
        self.assertEqual(status_int, res.status_int)

    def test_update_resource_with_cloud_attributes(self):
        self._test_update_resource_with_cloud_attributes()

    # 5. PUT /v2.0/resources/{resource_id}/cloud_attributes
    def _extend_cloud_attributes(self, cloud_attributes, ext_cloud_attr,
                                 exp_cloud_attr):
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        subnet = self.deserialize(self.fmt, res)
        req = self.new_update_request("subnets",
                                      data=ext_cloud_attr,
                                      id=subnet['subnet']['id'],
                                      subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        res = self.deserialize(self.fmt, res)
        self.assertEqual(exp_cloud_attr, res['cloud_attributes'])

    def _extend_cloud_attributes_error(self, cloud_attributes, ext_cloud_attr,
                                       status_int):
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        subnet = self.deserialize(self.fmt, res)
        req = self.new_update_request("subnets",
                                      data=ext_cloud_attr,
                                      id=subnet['subnet']['id'],
                                      subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        self.assertEqual(status_int, res.status_int)

    def test_extend_cloud_attributes(self):
        self._test_extend_cloud_attributes()

    # 6. DELETE /v2.0/resources/{resource_id}/cloud_attributes/key
    def _remove_cloud_attributes_by_key(self, cloud_attributes, key_to_delete,
                                        exp_cloud_attr):
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        subnet = self.deserialize(self.fmt, res)
        req = self.new_delete_request("subnets",
                                      subnet['subnet']['id'],
                                      subresource='cloud_attributes',
                                      sub_id=key_to_delete)
        req.get_response(self._api_for_resource("cloud_attributes"))
        req = self.new_show_request("subnets", subnet['subnet']['id'],
                                    subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        res = self.deserialize(self.fmt, res)
        self.assertEqual(exp_cloud_attr, res['cloud_attributes'])

    def _remove_cloud_attributes_by_key_error(self, cloud_attributes,
                                              key_to_delete):
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        subnet = self.deserialize(self.fmt, res)
        req = self.new_delete_request("subnets",
                                      subnet['subnet']['id'],
                                      subresource='cloud_attributes',
                                      sub_id=key_to_delete)
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        self.assertEqual(400, res.status_int)

    def test_remove_cloud_attributes_by_key(self):
        self._test_remove_cloud_attributes_by_key()

    # 7. DELETE /v2.0/resources/{resource_id}/cloud_attributes
    def _remove_cloud_attributes(self, cloud_attributes, del_cloud_attr,
                                 exp_cloud_attr):
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        subnet = self.deserialize(self.fmt, res)
        req = self.new_delete_request("subnets",
                                      subnet['subnet']['id'],
                                      data=del_cloud_attr,
                                      subresource='cloud_attributes')
        req.get_response(self._api_for_resource("cloud_attributes"))
        req = self.new_show_request("subnets", subnet['subnet']['id'],
                                    subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        res = self.deserialize(self.fmt, res)
        self.assertEqual(exp_cloud_attr, res['cloud_attributes'])

    def _remove_cloud_attributes_error(self, cloud_attributes, del_cloud_attr,
                                       status_int):
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        subnet = self.deserialize(self.fmt, res)
        req = self.new_delete_request("subnets",
                                      subnet['subnet']['id'],
                                      data=del_cloud_attr,
                                      subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        self.assertEqual(status_int, res.status_int)

    def _remove_all_cloud_attributes(self, cloud_attributes):
        res = self._create_subnet_with_cloud_attributes(cloud_attributes)
        subnet = self.deserialize(self.fmt, res)
        req = self.new_delete_request("subnets",
                                      subnet['subnet']['id'],
                                      subresource='cloud_attributes')
        req.get_response(self._api_for_resource("cloud_attributes"))
        req = self.new_show_request("subnets", subnet['subnet']['id'],
                                    subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        res = self.deserialize(self.fmt, res)
        self.assertEqual({}, res['cloud_attributes'])

    def test_remove_cloud_attributes(self):
        self._test_remove_cloud_attributes()


class AgentTestExtensionManager(object):

    def get_resources(self):
        return (agent.Agent.get_resources() +
                cloud_attribute.Cloud_attribute.get_resources())

    def get_actions(self):
        return []

    def get_request_extensions(self):
        return []


# This plugin class is just for testing
class TestAgentPlugin(db_base_plugin_v2.NeutronDbPluginV2,
                      agents_db.AgentDbMixin):
    supported_extension_aliases = ["agent"]


class AgentDBTestMixIn(object):

    def _list_agents(self, expected_res_status=None,
                     neutron_context=None,
                     query_string=None):
        agent_res = self._list('agents',
                               neutron_context=neutron_context,
                               query_params=query_string)
        if expected_res_status:
            self.assertEqual(expected_res_status, agent_res.status_int)
        return agent_res

    def _register_agent_states(self):
        """Register two L3 agents and two DHCP agents."""
        l3_hosta = helpers._get_l3_agent_dict(
            L3_HOSTA, constants.L3_AGENT_MODE_LEGACY)
        helpers.register_l3_agent(host=L3_HOSTA)

        res = [l3_hosta]
        return res


class AgentCloudAttributesTestCase(AgentDBTestMixIn,
                      test_db_base_plugin_v2.NeutronDbPluginV2TestCase,
                      CloudAttributesTestBaseCase):
    fmt = 'json'

    def setUp(self):
        plugin = 'neutron.tests.unit.extensions.test_agent.TestAgentPlugin'
        svc_plugins = ('neutron.services.cloud_attribute.plugin.'
                       'CloudAttributePlugin',)
        # for these tests we need to enable overlapping ips
        cfg.CONF.set_default('allow_overlapping_ips', True)
        ext_mgr = AgentTestExtensionManager()
        super(AgentCloudAttributesTestCase, self).setUp(
            plugin=plugin, ext_mgr=ext_mgr, service_plugins=svc_plugins)
        self.adminContext = context.get_admin_context()

    # 1. POST /v2.0/resources/{resource_id}
    def _create_agent_with_cloud_attributes(self, cloud_attributes):
        self._register_agent_states()
        agents = self._list_agents(
            query_string='binary=neutron-l3-agent&host=' + L3_HOSTA)
        self.assertEqual(1, len(agents['agents']))
        agent_id = agents['agents'][0]['id']
        new_agent = {}
        new_agent['agent'] = {}
        new_agent['agent']['cloud_attributes'] = cloud_attributes
        req = self.new_update_request('agents', new_agent, agent_id)
        res = req.get_response(self._api_for_resource('agents'))
        return res

    def _create_resource_with_cloud_attributes(
            self, cloud_attributes=None, exp_cloud_attr=None):
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        res = self.deserialize(self.fmt, res)
        if exp_cloud_attr:
            self.assertEqual(exp_cloud_attr,
                            res['agent']['cloud_attributes'])
        else:
            if not cloud_attributes:
                cloud_attributes = {}
            self.assertEqual(cloud_attributes,
                            res['agent']['cloud_attributes'])

    def _create_resource_with_cloud_attributes_error(
            self, cloud_attributes, status_int):
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        self.assertEqual(status_int, res.status_int)

    def test_create_resource_with_cloud_attributes(self):
        self._test_create_resource_with_cloud_attributes()

    # 2. GET /v2.0/resources/{resource_id}/cloud_attributes
    def _get_cloud_attributes(self):
        cloud_attributes = {'a': '1', 'n': '2', 'c': '3'}
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        agent = self.deserialize(self.fmt, res)
        req = self.new_show_request("agents", agent['agent']['id'],
                                    subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        res = self.deserialize(self.fmt, res)
        self.assertEqual(cloud_attributes, res['cloud_attributes'])

    def test_get_cloud_attributes(self):
        self._test_get_cloud_attributes()

    # 3. GET /v2.0/resources/{resource_id}/cloud_attributes/key
    def _get_cloud_attributes_by_key(self):
        cloud_attributes = {'a': '1', 'b': '2', 'c': '3'}
        key = 'b'
        exp_cloud_attr = {'b': '2'}
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        agent = self.deserialize(self.fmt, res)
        req = self.new_show_request("agents", agent['agent']['id'],
                                    subresource='cloud_attributes',
                                    sub_id=key)
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        res = self.deserialize(self.fmt, res)
        self.assertEqual(exp_cloud_attr, res)

    def test_get_cloud_attributes_by_key(self):
        self._test_get_cloud_attributes_by_key()

    # 4. PUT /v2.0/resources/{resource_id}
    def _update_resource_with_cloud_attributes(
            self, cloud_attributes, update_cloud_attr, exp_cloud_attr):
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        agent = self.deserialize(self.fmt, res)
        data = {'agent': {'cloud_attributes': update_cloud_attr}}
        req = self.new_update_request('agents', data, agent['agent']['id'])
        res = req.get_response(self.api)
        res = self.deserialize(self.fmt, res)
        self.assertEqual(exp_cloud_attr, res['agent']['cloud_attributes'])

    def _update_resource_with_cloud_attributes_error(
            self, cloud_attributes, update_cloud_attr, status_int):
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        agent = self.deserialize(self.fmt, res)
        data = {'agent': {'cloud_attributes': update_cloud_attr}}
        req = self.new_update_request('agents', data, agent['agent']['id'])
        res = req.get_response(self.api)
        self.assertEqual(status_int, res.status_int)

    def test_update_resource_with_cloud_attribtues(self):
        self._test_update_resource_with_cloud_attributes()

    # 5. PUT /v2.0/resources/{resource_id}/cloud_attributes
    def _extend_cloud_attributes(self, cloud_attributes, ext_cloud_attr,
                                 exp_cloud_attr):
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        agent = self.deserialize(self.fmt, res)
        req = self.new_update_request("agents",
                                      data=ext_cloud_attr,
                                      id=agent['agent']['id'],
                                      subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        res = self.deserialize(self.fmt, res)
        self.assertEqual(exp_cloud_attr, res['cloud_attributes'])

    def _extend_cloud_attributes_error(self, cloud_attributes, ext_cloud_attr,
                                       status_int):
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        agent = self.deserialize(self.fmt, res)
        req = self.new_update_request("agents",
                                      data=ext_cloud_attr,
                                      id=agent['agent']['id'],
                                      subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        self.assertEqual(status_int, res.status_int)

    def test_extend_cloud_attributes(self):
        self._test_extend_cloud_attributes()

    # 6. DELETE /v2.0/resources/{resource_id}/cloud_attributes/key
    def _remove_cloud_attributes_by_key(self, cloud_attributes, key_to_delete,
                                        exp_cloud_attr):
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        agent = self.deserialize(self.fmt, res)
        req = self.new_delete_request("agents",
                                      agent['agent']['id'],
                                      subresource='cloud_attributes',
                                      sub_id=key_to_delete)
        req.get_response(self._api_for_resource("cloud_attributes"))
        req = self.new_show_request("agents", agent['agent']['id'],
                                    subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        res = self.deserialize(self.fmt, res)
        self.assertEqual(exp_cloud_attr, res['cloud_attributes'])

    def _remove_cloud_attributes_by_key_error(self, cloud_attributes,
                                              key_to_delete):
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        agent = self.deserialize(self.fmt, res)
        req = self.new_delete_request("agents",
                                      agent['agent']['id'],
                                      subresource='cloud_attributes',
                                      sub_id=key_to_delete)
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        self.assertEqual(400, res.status_int)

    def test_remove_cloud_attributes_by_key(self):
        self._test_remove_cloud_attributes_by_key()

    # 7. DELETE /v2.0/resources/{resource_id}/cloud_attributes
    def _remove_cloud_attributes(self, cloud_attributes, del_cloud_attr,
                                 exp_cloud_attr):
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        agent = self.deserialize(self.fmt, res)
        req = self.new_delete_request("agents",
                                      agent['agent']['id'],
                                      data=del_cloud_attr,
                                      subresource='cloud_attributes')
        req.get_response(self._api_for_resource("cloud_attributes"))
        req = self.new_show_request("agents", agent['agent']['id'],
                                    subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        res = self.deserialize(self.fmt, res)
        self.assertEqual(exp_cloud_attr, res['cloud_attributes'])

    def _remove_cloud_attributes_error(self, cloud_attributes, del_cloud_attr,
                                       status_int):
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        agent = self.deserialize(self.fmt, res)
        req = self.new_delete_request("agents",
                                      agent['agent']['id'],
                                      data=del_cloud_attr,
                                      subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        self.assertEqual(status_int, res.status_int)

    def _remove_all_cloud_attributes(self, cloud_attributes):
        res = self._create_agent_with_cloud_attributes(cloud_attributes)
        agent = self.deserialize(self.fmt, res)
        req = self.new_delete_request("agents",
                                      agent['agent']['id'],
                                      subresource='cloud_attributes')
        req.get_response(self._api_for_resource("cloud_attributes"))
        req = self.new_show_request("agents", agent['agent']['id'],
                                    subresource='cloud_attributes')
        res = req.get_response(self._api_for_resource("cloud_attributes"))
        res = self.deserialize(self.fmt, res)
        self.assertEqual({}, res['cloud_attributes'])

    def test_remove_cloud_attributes(self):
        self._test_remove_cloud_attributes()
