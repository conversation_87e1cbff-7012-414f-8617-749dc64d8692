# Copyright 2024 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add cloud attributes to network

Revision ID: ae9ebf736339
Revises: 8c4ba78d0ff5
Create Date: 2024-05-01 14:47:09.683689

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'ae9ebf736339'
down_revision = '8c4ba78d0ff5'


def upgrade():
    exist_network_cloud_attributes = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for table in insp.get_table_names():
        if table == 'network_cloud_attributes':
            exist_network_cloud_attributes = True
            break
    if not exist_network_cloud_attributes:
        op.create_table(
            'network_cloud_attributes',
            sa.Column('network_id', sa.String(length=36), nullable=False),
            sa.Column('cloud_attributes', sa.String(4095)),
            sa.ForeignKeyConstraint(['network_id'], ['networks.id'],
                                    ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('network_id')
        )
