# Copyright 2021 OpenStack Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.
#

from neutron_lib.callbacks import registry
from neutron_lib import exceptions
from oslo_log import log as logging

from neutron._i18n import _
from neutron.db import _resource_extend as resource_extend
from neutron.db import api as db_api
from neutron.extensions.cloud_attribute import validate_cloud_attributes
from neutron.objects import agent as agent_obj

LOG = logging.getLogger(__name__)


@resource_extend.has_resource_extenders
@registry.has_registry_receivers
class AgentCloudAttributesMixin(object):
    """Mixin class of agent's cloud_attributes."""

    def _get_agent_cloud_attrs(self, context, agent_id):
        cloud_attr_obj = agent_obj.AgentCloudAttributes.get_object(
            context, agent_id=agent_id)
        if cloud_attr_obj is None:
            msg = (_("The agent %(id)'s cloud_attributes not found."))
            raise exceptions.InvalidInput(error_message=msg)
        return cloud_attr_obj

    def _create_agent_cloud_attributes(self, context, agent, cloud_attrs):
        validate_cloud_attributes(cloud_attrs)
        with db_api.context_manager.writer.using(context):
            agent_cloud_obj = agent_obj.AgentCloudAttributes(
                context, agent_id=agent['id'],
                cloud_attributes=cloud_attrs)
            agent_cloud_obj.create()
        return agent_cloud_obj.cloud_attributes

    def _update_agent_cloud_attributes(self, context, id, new_attrs, agent):
        if not new_attrs:
            return
        validate_cloud_attributes(new_attrs)
        if 'cloud_attributes' not in agent:
            cloud_attributes = self._create_agent_cloud_attributes(
                context, agent, new_attrs)
            LOG.info("Create agent %s cloud attributes.", id)
        else:
            cloud_attributes = agent.get('cloud_attributes')
            for k, v in new_attrs.items():
                if k not in cloud_attributes.keys():
                    cloud_attributes[k] = v
                else:
                    LOG.info("Update agent %(agent_id)s cloud attribute "
                             "%(key)s's value, the current cloud "
                             "attribute is %(key)s:%(value)s.",
                             {'agent_id': id,
                              'key': k,
                              'value': cloud_attributes[k]})
                    cloud_attributes[k] = v
            validate_cloud_attributes(cloud_attributes)
            with db_api.context_manager.writer.using(context):
                agent_attrs_obj = self._get_agent_cloud_attrs(context, id)
                agent_attrs_obj.update_fields(
                    {'cloud_attributes': cloud_attributes})
                agent_attrs_obj.update()
        LOG.info("Finish update agent %s cloud attributes.", id)
        return cloud_attributes
