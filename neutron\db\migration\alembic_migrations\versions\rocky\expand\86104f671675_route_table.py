# Copyright 2023 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add routetables, default_routetable,
routetable_routes, routetablesubnetbindings

Revision ID: 86104f671675
Revises: eb4819f04fb2
Create Date: 2024-10-20 10:55:16.764635

"""

from alembic import op
import sqlalchemy as sa

from neutron_lib.db import constants


# revision identifiers, used by Alembic.
revision = '86104f671675'
down_revision = 'fffa5194a987'


def upgrade():
    exist_routetables = False
    exist_default_routetable = False
    exist_routetable_routes = False
    exist_routetablesubnetbindings = False

    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for table in insp.get_table_names():
        if table == 'routetables':
            exist_routetables = True
        if table == 'default_routetable':
            exist_default_routetable = True
        if table == 'routetable_routes':
            exist_routetable_routes = True
        if table == 'routetablesubnetbindings':
            exist_routetablesubnetbindings = True

    if not exist_routetables:
        op.create_table(
            'routetables',
            sa.Column('id', sa.String(length=constants.UUID_FIELD_SIZE),
                      nullable=False),
            sa.Column('name', sa.String(length=255), nullable=True),
            sa.Column('router_id',
                      sa.String(length=constants.UUID_FIELD_SIZE),
                      nullable=False),
            sa.Column('project_id',
                      sa.String(length=constants.PROJECT_ID_FIELD_SIZE),
                      nullable=True, index=True),
            sa.Column('table_id', sa.Integer(), nullable=False),
            sa.Column('standard_attr_id', sa.BigInteger(), nullable=False),
            sa.PrimaryKeyConstraint('id'),
            sa.ForeignKeyConstraint(['router_id'], ['routers.id'],
                                    ondelete="CASCADE"),
            sa.ForeignKeyConstraint(['standard_attr_id'],
                                    ['standardattributes.id'],
                                    ondelete='CASCADE'),
            sa.UniqueConstraint('standard_attr_id')
        )

    if not exist_default_routetable:
        op.create_table(
            'default_routetable',
            sa.Column('router_id', sa.String(length=36), nullable=False),
            sa.Column('routetable_id', sa.String(length=36), nullable=False),
            sa.PrimaryKeyConstraint('router_id'),
            sa.ForeignKeyConstraint(['routetable_id'], ['routetables.id'],
                                    ondelete="CASCADE"),
            sa.ForeignKeyConstraint(['router_id'], ['routers.id']))

    if not exist_routetable_routes:
        op.create_table(
            'routetable_routes',
            sa.Column('type', sa.String(length=30), nullable=False),
            sa.Column('destination', sa.String(length=64), nullable=False),
            sa.Column('nexthop', sa.String(length=64), nullable=False),
            sa.Column('routetable_id', sa.String(length=36), nullable=False),
            sa.ForeignKeyConstraint(['routetable_id'], ['routetables.id'],
                                    ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('destination', 'routetable_id')
        )

    if not exist_routetablesubnetbindings:
        op.create_table(
            'routetablesubnetbindings',
            sa.Column('routetable_id', sa.String(length=36), nullable=False),
            sa.Column('subnet_id', sa.String(length=36), nullable=False),
            sa.ForeignKeyConstraint(['subnet_id'], ['subnets.id'],
                                    ondelete='CASCADE'),
            sa.ForeignKeyConstraint(['routetable_id'], ['routetables.id'],
                                    ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('subnet_id', 'routetable_id')
        )
