# Copyright 2020 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add_ha_hostname_type_to_metering_vpcs

Revision ID: fd1ce1d5489a
Revises: 58fa12815e15
Create Date: 2020-09-07 14:48:00.045805

"""

from alembic import op
from neutron_lib.db import constants as db_const
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'b5e8c44184b3'
down_revision = 'ba69e870128f'


def upgrade():
    table_name = 'meteringvpcs'
    existColumn = 0b0
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for column in insp.get_columns(table_name):
        existColumn |= 0b1 if column['name'] == 'ha' else 0b0
        existColumn |= 0b10 if column['name'] == 'hostname' else 0b00
        existColumn |= 0b100 if column['name'] == 'type' else 0b000
        existColumn |= 0b1000 if column['name'] == 'source_ip' else 0b0000

    if not existColumn & 0b1:
        op.add_column(
            'meteringvpcs',
            sa.Column(
                'ha',
                sa.Boolean(),
                nullable=False))
    if not existColumn & 0b10:
        op.add_column(
            'meteringvpcs',
            sa.Column(
                'hostname',
                sa.String(
                    length=db_const.NAME_FIELD_SIZE),
                nullable=False))
    if not existColumn & 0b100:
        op.add_column(
            'meteringvpcs',
            sa.Column(
                'type',
                sa.String(
                    length=db_const.STATUS_FIELD_SIZE),
                nullable=True))
    if existColumn & 0b1000:
        op.drop_column('meteringvpcs', 'source_ip')
