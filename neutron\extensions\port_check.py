# Copyright 2023 Acronis
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
# implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import abc
import os

from neutron_lib.api import converters
from neutron_lib.api import extensions as api_extensions
from neutron_lib.api import faults
from neutron_lib.api import validators
from neutron_lib import constants
from neutron_lib import exceptions as n_exc
from neutron_lib.plugins import directory
from neutron_lib.services import base as service_base
import six

from neutron._i18n import _
from neutron.api import extensions
from neutron.api.v2 import resource
from neutron import wsgi


PORT_CHECK = 'port-check'
extensions.append_api_extensions_path([os.path.dirname(__file__)])


RESOURCE_ATTRIBUTE_MAP = {
    'check': {
        'port_id': {'allow_post': False,
                    'allow_put': False},
        'protocol': {'allow_post': False,
                     'allow_put': False,
                     'validate': {
                         'type:values': ['ip', 'arp', 'tcp', 'udp', 'icmp',
                                         'tcp6', 'udp6', 'icmp6']}},
        'direction': {'allow_post': False,
                      'allow_put': False,
                      'validate': {'type:values': ['ingress', 'egress']}},
        'ip_src': {'allow_post': False,
                   'allow_put': False,
                   'validate': {'type:ip_address': None}},
        'ip_dst': {'allow_post': False,
                   'allow_put': False,
                   'validate': {'type:ip_address': None}},
        'mac_src': {'allow_post': False,
                    'allow_put': False,
                    'validate': {'type:mac_address': None}},
        'mac_dst': {'allow_post': False,
                    'allow_put': False,
                    'validate': {'type:mac_address': None}},
        'port_src': {'allow_post': False,
                     'allow_put': False,
                     'convert_to': converters.convert_to_int_if_not_none,
                     'validate': {
                         'type:range_or_none': [0, constants.PORT_MAX]}},
        'port_dst': {'allow_post': False,
                     'allow_put': False,
                     'convert_to': converters.convert_to_int_if_not_none,
                     'validate': {
                         'type:range_or_none': [0, constants.PORT_MAX]}},
        'arp_op': {'allow_post': False,
                   'allow_put': False,
                   'convert_to': converters.convert_to_int_if_not_none,
                   'validate': {'type:range_or_none': [0, 4]}},
        'physical_dev': {'allow_post': False,
                         'allow_put': False},
    }
}


class PortCheckController(wsgi.Controller):
    def create(self, request, **kwargs):
        if not request.context.is_admin:
            reason = _("Only admin is authorized to access port check API")
            raise n_exc.AdminRequired(reason=reason)
        port_id = kwargs['body'].get('port_id', None)
        host_id = kwargs['body'].get('host_id', None)
        plugin = directory.get_plugin(PORT_CHECK)
        return plugin.port_check(request.context, port_id, host_id)

    def index(self, request, **kwargs):
        if not request.context.is_admin:
            reason = _("Only admin is authorized to access port check API")
            raise n_exc.AdminRequired(reason=reason)
        plugin = directory.get_plugin(PORT_CHECK)
        kwargs = self._process_request(request, **kwargs)
        port_id = kwargs.pop('port_id')
        return plugin.flow_trace(request.context, port_id, **kwargs)

    def _process_request(self, request, **kwargs):
        attr_map = RESOURCE_ATTRIBUTE_MAP.get('check')
        for k, v in request.GET.items():
            if k not in attr_map:
                continue
            for rule in attr_map[k].get('validate', []):
                validator = validators.get_validator(rule)
                res = validator(v, attr_map[k].get('validate')[rule])
                if res:
                    msg_dict = dict(attr=k, reason=res)
                    msg = _("Invalid input for %(attr)s. "
                            "Reason: %(reason)s.") % msg_dict
                    raise n_exc.InvalidInput(error_message=msg)
            kwargs[k] = v
        return kwargs


class Port_check(api_extensions.ExtensionDescriptor):

    @classmethod
    def get_name(cls):
        return "Port-check extenstion"

    @classmethod
    def get_alias(cls):
        return "port-check"

    @classmethod
    def get_description(cls):
        return "Port-check extension"

    @classmethod
    def get_updated(cls):
        return "2022-04-22T10:00:00-00:00"

    @classmethod
    def get_resources(cls):
        """Returns Ext Resources."""
        controller = resource.Resource(PortCheckController(), faults.FAULT_MAP)
        ext = extensions.ResourceExtension('check', controller)
        return [ext]

    @classmethod
    def get_plugin_interface(cls):
        return PortCheckPluginBase


@six.add_metaclass(abc.ABCMeta)
class PortCheckPluginBase(service_base.ServicePluginBase):

    def get_plugin_type(self):
        return PORT_CHECK

    def get_plugin_description(self):
        return 'Port-check plugin'

    @abc.abstractmethod
    def port_check(self, context, port_id=None, host_id=None):
        pass

    @abc.abstractmethod
    def flow_trace(self, context, port_id, **kwargs):
        pass
