# Copyright 2021 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add type to metering labels

Revision ID: 75de5ad18d3d
Revises: d72db3e25539
Create Date: 2021-03-11 09:01:27.723471

"""

from alembic import op
import sqlalchemy as sa

from neutron.common import constants as n_const

# revision identifiers, used by Alembic.
revision = '75de5ad18d3d'
down_revision = 'c613d0b82681'


def upgrade():
    table_name = 'meteringlabels'
    existColumn = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for column in insp.get_columns(table_name):
        if column['name'] == 'type':
            existColumn = True

    if not existColumn:
        op.add_column(
            table_name,
            sa.Column('type', sa.Enum(n_const.METERING_TYPE_EIP,
                                      n_const.METERING_TYPE_PF_EIP,
                                      n_const.METERING_TYPE_SNAT_EIP),
                      nullable=False,
                      server_default=n_const.METERING_TYPE_EIP))
