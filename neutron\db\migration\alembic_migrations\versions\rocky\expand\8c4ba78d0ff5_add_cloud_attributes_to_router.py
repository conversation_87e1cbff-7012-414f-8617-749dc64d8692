# Copyright 2024 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add cloud attributes to router

Revision ID: 8c4ba78d0ff5
Revises: efa57beb2abe
Create Date: 2024-04-20 16:22:40.659476

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '8c4ba78d0ff5'
down_revision = '661199622a97'


def upgrade():
    table_name = 'router_extra_attributes'
    exist_column = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for column in insp.get_columns(table_name):
        if column['name'] == 'cloud_attributes':
            exist_column = True
    if not exist_column:
        cloud_attributes_col = sa.Column('cloud_attributes', sa.String(4095))
        op.add_column(table_name, cloud_attributes_col)
