#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

import mock

from neutron_lib import context
from oslo_config import cfg
from oslo_utils import uuidutils
from webob import exc

from neutron.tests.unit.api import test_extensions
from neutron.tests.unit.extensions import \
    test_expose_port_forwarding_in_fip as test_fip_pf
from neutron.tests.unit.extensions import test_l3

_uuid = uuidutils.generate_uuid


class FloatingIPPorForwardingTestCase(test_l3.L3BaseForIntTests,
                                      test_l3.L3NatTestCaseMixin):
    fmt = 'json'

    def setUp(self):
        mock.patch('neutron.api.rpc.handlers.resources_rpc.'
                   'ResourcesPushRpcApi').start()
        svc_plugins = (test_fip_pf.PF_PLUGIN_NAME, test_fip_pf.L3_PLUGIN,
                       'neutron.services.qos.qos_plugin.QoSPlugin')
        ext_mgr = test_fip_pf.ExtendFipPortForwardingExtensionManager()
        super(FloatingIPPorForwardingTestCase, self).setUp(
            ext_mgr=ext_mgr, service_plugins=svc_plugins)
        self.ext_api = test_extensions.setup_extensions_middleware(ext_mgr)

    def _create_fip_port_forwarding(self, fmt,
                                    floating_ip_id,
                                    external_port,
                                    internal_port,
                                    protocol,
                                    internal_ip_address,
                                    internal_port_id,
                                    tenant_id=None,
                                    external_port_range=None,
                                    internal_port_range=None):
        tenant_id = tenant_id or _uuid()
        data = {'port_forwarding': {
            "protocol": protocol,
            "internal_ip_address": internal_ip_address,
            "internal_port_id": internal_port_id}
        }
        if external_port_range and internal_port_range:
            data['port_forwarding'][
                'internal_port_range'] = internal_port_range
            data['port_forwarding'][
                'external_port_range'] = external_port_range
        else:
            data['port_forwarding']['internal_port'] = internal_port
            data['port_forwarding']['external_port'] = external_port

        fip_pf_req = self._req(
            'POST', 'floatingips', data,
            fmt or self.fmt, id=floating_ip_id,
            subresource='port_forwardings')

        fip_pf_req.environ['neutron.context'] = context.Context(
            '', tenant_id, is_admin=True)

        return fip_pf_req.get_response(self.ext_api)

    def _update_fip_port_forwarding(self, fmt, floating_ip_id,
                                    port_forwarding_id, **kwargs):
        port_forwarding = {}
        for k, v in kwargs.items():
            port_forwarding[k] = v
        data = {'port_forwarding': port_forwarding}

        fip_pf_req = self._req(
            'PUT', 'floatingips', data,
            fmt or self.fmt, id=floating_ip_id,
            sub_id=port_forwarding_id,
            subresource='port_forwardings')

        return fip_pf_req.get_response(self.ext_api)

    def test_create_floatingip_port_forwarding_with_port_number_0(self):
        with self.network() as ext_net:
            network_id = ext_net['network']['id']
            self._set_net_external(network_id)
            with self.subnet(ext_net, cidr='**********/24'), \
                    self.router() as router, \
                    self.subnet(cidr='********/24') as private_subnet, \
                    self.port(private_subnet) as port:
                self._add_external_gateway_to_router(
                    router['router']['id'],
                    network_id)
                self._router_interface_action(
                    'add', router['router']['id'],
                    private_subnet['subnet']['id'],
                    None)
                fip = self._make_floatingip(
                    self.fmt,
                    network_id)
                self.assertIsNone(fip['floatingip'].get('port_id'))
                res = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    2222, 0,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'])
                self.assertEqual(exc.HTTPBadRequest.code, res.status_int)

                res = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    0, 22,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'])
                self.assertEqual(exc.HTTPBadRequest.code, res.status_int)

    def test_create_floatingip_port_forwarding_with_ranges(self):
        internal_port_range = '22:24'
        external_port_range = '2222:2224'
        with self.network() as ext_net:
            network_id = ext_net['network']['id']
            self._set_net_external(network_id)
            with self.subnet(ext_net, cidr='**********/24'), \
                    self.router() as router, \
                    self.subnet(cidr='********/24') as private_subnet, \
                    self.port(private_subnet) as port:
                self._add_external_gateway_to_router(
                    router['router']['id'],
                    network_id)
                self._router_interface_action(
                    'add', router['router']['id'],
                    private_subnet['subnet']['id'],
                    None)
                fip = self._make_floatingip(
                    self.fmt,
                    network_id)
                self.assertIsNone(fip['floatingip'].get('port_id'))
                res = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range=internal_port_range,
                    external_port_range=external_port_range)
                self.assertEqual(exc.HTTPCreated.code, res.status_int)
                pf_body = self.deserialize(self.fmt, res)
                self.assertEqual(
                    internal_port_range,
                    pf_body['port_forwarding']['internal_port_range'])
                self.assertEqual(
                    external_port_range,
                    pf_body['port_forwarding']['external_port_range'])

    def test_create_floatingip_port_forwarding_with_ranges_port_collisions(
            self):
        internal_port_range1 = '22:24'
        internal_port_range2 = '23:25'
        external_port_range1 = '2222:2224'
        external_port_range2 = '2223:2225'
        with self.network() as ext_net:
            network_id = ext_net['network']['id']
            self._set_net_external(network_id)
            with self.subnet(ext_net, cidr='**********/24'), \
                    self.router() as router, \
                    self.subnet(cidr='********/24') as private_subnet, \
                    self.port(private_subnet) as port:
                self._add_external_gateway_to_router(
                    router['router']['id'],
                    network_id)
                self._router_interface_action(
                    'add', router['router']['id'],
                    private_subnet['subnet']['id'],
                    None)
                fip = self._make_floatingip(
                    self.fmt,
                    network_id)
                self.assertIsNone(fip['floatingip'].get('port_id'))
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range=internal_port_range1,
                    external_port_range=external_port_range1)
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range=internal_port_range2,
                    external_port_range=external_port_range2)
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

                # CASE 1: Test whether internal port range is overlapped
                internal_port_1 = 55
                external_port_1 = 55
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    internal_port_1,
                    external_port_1,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    None, None)

                internal_port_range_3 = '55:56'
                external_port_range_3 = '55:56'
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range=internal_port_range_3,
                    external_port_range=external_port_range_3)
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

                # CASE 2: Test whether new internal port range can overlap
                # existed port range.
                internal_port_range_4 = '10:60'
                external_port_range_4 = '10:60'
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range=internal_port_range_4,
                    external_port_range=external_port_range_4)
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

    def test_create_fip_pf_port_ranges_collisions(self):
        cfg.CONF.set_override(
            "allow_port_forwarding_internal_port_range_overlapping", False)
        cfg.CONF.set_override(
            "port_forwarding_check_fast", False)
        self._test_create_fip_pf_port_ranges_collisions()

        cfg.CONF.set_override(
            "allow_port_forwarding_internal_port_range_overlapping", False)
        cfg.CONF.set_override(
            "port_forwarding_check_fast", True)
        self._test_create_fip_pf_port_ranges_collisions()

    def _test_create_fip_pf_port_ranges_collisions(self):
        with self.network() as ext_net:
            network_id = ext_net['network']['id']
            self._set_net_external(network_id)
            with self.subnet(ext_net, cidr='**********/24'), \
                    self.router() as router, \
                    self.subnet(cidr='********/24') as private_subnet, \
                    self.port(private_subnet) as port:
                self._add_external_gateway_to_router(
                    router['router']['id'],
                    network_id)
                self._router_interface_action(
                    'add', router['router']['id'],
                    private_subnet['subnet']['id'],
                    None)
                fip = self._make_floatingip(
                    self.fmt,
                    network_id)
                self.assertIsNone(fip['floatingip'].get('port_id'))

                # CASE 1: Test creating pf with duplicated single internal port
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='100:100',
                    external_port_range='1000:1000')
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='100:100',
                    external_port_range='1001:1001')
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

                # CASE 2: Test creating pf with duplicated internal port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='102:105',
                    external_port_range='1002:1005')
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='102:105',
                    external_port_range='1007:1010')
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

                # CASE 3: Test creating pf with overlapping internal port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='106:110',
                    external_port_range='1016:1020')
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='108:112',
                    external_port_range='1021:1025')
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

                # CASE 4: Test creating pf with duplicated single external port
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='115:115',
                    external_port_range='1026:1026')
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='116:116',
                    external_port_range='1026:1026')
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

                # CASE 5: Test creating pf with duplicated external port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='117:120',
                    external_port_range='1032:1035')
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='121:124',
                    external_port_range='1032:1035')
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

                # CASE 6: Test creating pf with overlapping internal port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='131:135',
                    external_port_range='1036:1040')
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='136:140',
                    external_port_range='1038:1042')
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

    def test_create_fip_pf_allow_internal_port_ranges_collisions(self):
        cfg.CONF.set_override(
            "allow_port_forwarding_internal_port_range_overlapping", True)
        cfg.CONF.set_override(
            "port_forwarding_check_fast", False)
        self._test_create_fip_pf_allow_internal_port_ranges_collisions()

        cfg.CONF.set_override(
            "allow_port_forwarding_internal_port_range_overlapping", True)
        cfg.CONF.set_override(
            "port_forwarding_check_fast", True)
        self._test_create_fip_pf_allow_internal_port_ranges_collisions()

    def _test_create_fip_pf_allow_internal_port_ranges_collisions(self):
        with self.network() as ext_net:
            network_id = ext_net['network']['id']
            self._set_net_external(network_id)
            with self.subnet(ext_net, cidr='**********/24'), \
                    self.router() as router, \
                    self.subnet(cidr='********/24') as private_subnet, \
                    self.port(private_subnet) as port:
                self._add_external_gateway_to_router(
                    router['router']['id'],
                    network_id)
                self._router_interface_action(
                    'add', router['router']['id'],
                    private_subnet['subnet']['id'],
                    None)
                fip = self._make_floatingip(
                    self.fmt,
                    network_id)
                self.assertIsNone(fip['floatingip'].get('port_id'))

                # CASE 1: Test creating pf with duplicated single internal port
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='100:100',
                    external_port_range='1000:1000')
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='100:100',
                    external_port_range='1001:1001')
                self.assertEqual(exc.HTTPCreated.code,
                                 response.status_int)

                # CASE 2: Test creating pf with duplicated internal port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='102:105',
                    external_port_range='1002:1005')
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='102:105',
                    external_port_range='1007:1010')
                self.assertEqual(exc.HTTPCreated.code,
                                 response.status_int)

                # CASE 3: Test creating pf with overlapping internal port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='106:110',
                    external_port_range='1016:1020')
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='108:112',
                    external_port_range='1021:1025')
                self.assertEqual(exc.HTTPCreated.code,
                                 response.status_int)

                # CASE 4: Test creating pf with duplicated single external port
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='115:115',
                    external_port_range='1026:1026')
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='116:116',
                    external_port_range='1026:1026')
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

                # CASE 5: Test creating pf with duplicated external port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='117:120',
                    external_port_range='1032:1035')
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='121:124',
                    external_port_range='1032:1035')
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

                # CASE 6: Test creating pf with overlapping internal port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='131:135',
                    external_port_range='1036:1040')
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='136:140',
                    external_port_range='1038:1042')
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

    def _create_two_port_forwardings(self):
        with self.network() as ext_net:
            network_id = ext_net['network']['id']
            self._set_net_external(network_id)
            with self.subnet(ext_net, cidr='**********/24'), \
                    self.router() as router, \
                    self.subnet(cidr='********/24') as private_subnet, \
                    self.port(private_subnet) as port:
                self._add_external_gateway_to_router(
                    router['router']['id'],
                    network_id)
                self._router_interface_action(
                    'add', router['router']['id'],
                    private_subnet['subnet']['id'],
                    None)
                fip = self._make_floatingip(
                    self.fmt,
                    network_id)
                self.assertIsNone(fip['floatingip'].get('port_id'))
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    123, 1234,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],)
                response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    124, 1235,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],)
                self.assertEqual(exc.HTTPBadRequest.code,
                                 response.status_int)

    def test_create_floatingip_port_forwarding_exceeds_the_port_limit(
            self):
        cfg.CONF.set_override("port_forwarding_limit_per_port", 1)
        self._create_two_port_forwardings()

    def test_create_floatingip_port_forwarding_exceeds_the_floatingip_limit(
            self):
        cfg.CONF.set_override("port_forwarding_limit_per_floatingip", 1)
        self._create_two_port_forwardings()

    def test_create_floatingip_port_forwarding_exceeds_the_router_limit(
            self):
        cfg.CONF.set_override("port_forwarding_limit_per_router", 1)
        self._create_two_port_forwardings()

    def test_update_floatingip_port_forwarding_with_dup_internal_port(self):
        with self.network() as ext_net:
            network_id = ext_net['network']['id']
            self._set_net_external(network_id)
            with self.subnet(ext_net, cidr='**********/24'), \
                    self.router() as router, \
                    self.subnet(cidr='********/24') as private_subnet, \
                    self.port(private_subnet) as port:
                self._add_external_gateway_to_router(
                    router['router']['id'],
                    network_id)
                self._router_interface_action(
                    'add', router['router']['id'],
                    private_subnet['subnet']['id'],
                    None)
                fip1 = self._make_floatingip(
                    self.fmt,
                    network_id)
                self.assertIsNone(fip1['floatingip'].get('port_id'))
                self._create_fip_port_forwarding(
                    self.fmt, fip1['floatingip']['id'],
                    2222, 22,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'])
                fip2 = self._make_floatingip(
                    self.fmt,
                    network_id)
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip2['floatingip']['id'],
                    2223, 23,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'])
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip2['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port': 22})
                self.assertEqual(exc.HTTPBadRequest.code,
                                 update_res.status_int)

    def test_update_fip_pf_port_range_collisions(self):
        cfg.CONF.set_override(
            "allow_port_forwarding_internal_port_range_overlapping", False)
        cfg.CONF.set_override(
            "port_forwarding_check_fast", False)
        self._test_update_fip_pf_port_range_collisions()

        cfg.CONF.set_override(
            "allow_port_forwarding_internal_port_range_overlapping", False)
        cfg.CONF.set_override(
            "port_forwarding_check_fast", True)
        self._test_update_fip_pf_port_range_collisions()

    def _test_update_fip_pf_port_range_collisions(self):
        with self.network() as ext_net:
            network_id = ext_net['network']['id']
            self._set_net_external(network_id)
            with self.subnet(ext_net, cidr='**********/24'), \
                    self.router() as router, \
                    self.subnet(cidr='********/24') as private_subnet, \
                    self.port(private_subnet) as port:
                self._add_external_gateway_to_router(
                    router['router']['id'],
                    network_id)
                self._router_interface_action(
                    'add', router['router']['id'],
                    private_subnet['subnet']['id'],
                    None)
                fip = self._make_floatingip(
                    self.fmt,
                    network_id)
                self.assertIsNone(fip['floatingip'].get('port_id'))

                # CASE 1: Test updating pf with duplicated single internal port
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='100:100',
                    external_port_range='1000:1000')
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='101:101',
                    external_port_range='1001:1001')
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port_range': '100:100',
                       'external_port_range': '1001:1001'})
                self.assertEqual(exc.HTTPBadRequest.code,
                                 update_res.status_int)

                # CASE 2: Test updating pf with duplicated internal port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='102:105',
                    external_port_range='1002:1005')
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='107:110',
                    external_port_range='1007:1010')
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port_range': '102:105',
                       'external_port_range': '1007:1010'})
                self.assertEqual(exc.HTTPBadRequest.code,
                                 update_res.status_int)

                # CASE 3: Test updating pf with overlapping internal port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='111:115',
                    external_port_range='1011:1015')
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='116:120',
                    external_port_range='1016:1020')
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port_range': '113:117',
                       'external_port_range': '1016:1020'})
                self.assertEqual(exc.HTTPBadRequest.code,
                                 update_res.status_int)

                # CASE 4: Test updating pf with duplicated single external port
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='121:121',
                    external_port_range='1021:1021')
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='122:122',
                    external_port_range='1022:1022')
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port_range': '122:122',
                       'external_port_range': '1021:1021'})
                self.assertEqual(exc.HTTPBadRequest.code,
                                 update_res.status_int)

                # CASE 5: Test updating pf with duplicated external port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='123:125',
                    external_port_range='1023:1025')
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='126:128',
                    external_port_range='1026:1028')
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port_range': '126:128',
                       'external_port_range': '1023:1025'})
                self.assertEqual(exc.HTTPBadRequest.code,
                                 update_res.status_int)

                # CASE 6: Test updating pf with overlapping external port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='131:135',
                    external_port_range='1031:1035')
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='136:140',
                    external_port_range='1036:1040')
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port_range': '136:140',
                       'external_port_range': '1033:1037'})
                self.assertEqual(exc.HTTPBadRequest.code,
                                 update_res.status_int)

    def test_update_fip_pf_allow_internal_port_range_collisions(self):
        cfg.CONF.set_override(
            "allow_port_forwarding_internal_port_range_overlapping", True)
        cfg.CONF.set_override(
            "port_forwarding_check_fast", False)
        self._test_update_fip_pf_allow_internal_port_range_collisions()

        cfg.CONF.set_override(
            "allow_port_forwarding_internal_port_range_overlapping", True)
        cfg.CONF.set_override(
            "port_forwarding_check_fast", True)
        self._test_update_fip_pf_allow_internal_port_range_collisions()

    def _test_update_fip_pf_allow_internal_port_range_collisions(self):
        with self.network() as ext_net:
            network_id = ext_net['network']['id']
            self._set_net_external(network_id)
            with self.subnet(ext_net, cidr='**********/24'), \
                    self.router() as router, \
                    self.subnet(cidr='********/24') as private_subnet, \
                    self.port(private_subnet) as port:
                self._add_external_gateway_to_router(
                    router['router']['id'],
                    network_id)
                self._router_interface_action(
                    'add', router['router']['id'],
                    private_subnet['subnet']['id'],
                    None)
                fip = self._make_floatingip(
                    self.fmt,
                    network_id)
                self.assertIsNone(fip['floatingip'].get('port_id'))

                # CASE 1: Test updating pf with duplicated single internal port
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='100:100',
                    external_port_range='1000:1000')
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='101:101',
                    external_port_range='1001:1001')
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port_range': '100:100',
                       'external_port_range': '1001:1001'})
                self.assertEqual(exc.HTTPOk.code,
                                 update_res.status_int)

                # CASE 2: Test updating pf with duplicated internal port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='102:105',
                    external_port_range='1002:1005')
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='107:110',
                    external_port_range='1007:1010')
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port_range': '102:105',
                       'external_port_range': '1007:1010'})
                self.assertEqual(exc.HTTPOk.code,
                                 update_res.status_int)

                # CASE 3: Test updating pf with overlapping internal port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='111:115',
                    external_port_range='1011:1015')
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='116:120',
                    external_port_range='1016:1020')
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port_range': '113:117',
                       'external_port_range': '1016:1020'})
                self.assertEqual(exc.HTTPOk.code,
                                 update_res.status_int)

                # CASE 4: Test updating pf with duplicated single external port
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='121:121',
                    external_port_range='1021:1021')
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='122:122',
                    external_port_range='1022:1022')
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port_range': '122:122',
                       'external_port_range': '1021:1021'})
                self.assertEqual(exc.HTTPBadRequest.code,
                                 update_res.status_int)

                # CASE 5: Test updating pf with duplicated external port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='123:125',
                    external_port_range='1023:1025')
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='126:128',
                    external_port_range='1026:1028')
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port_range': '126:128',
                       'external_port_range': '1023:1025'})
                self.assertEqual(exc.HTTPBadRequest.code,
                                 update_res.status_int)

                # CASE 6: Test updating pf with overlapping external port range
                self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='131:135',
                    external_port_range='1031:1035')
                fip_pf_response = self._create_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    None, None,
                    'tcp',
                    port['port']['fixed_ips'][0]['ip_address'],
                    port['port']['id'],
                    internal_port_range='136:140',
                    external_port_range='1036:1040')
                update_res = self._update_fip_port_forwarding(
                    self.fmt, fip['floatingip']['id'],
                    fip_pf_response.json['port_forwarding']['id'],
                    **{'internal_port_range': '136:140',
                       'external_port_range': '1033:1037'})
                self.assertEqual(exc.HTTPBadRequest.code,
                                 update_res.status_int)

    def test_create_update_floatingip_port_forwarding(self):
        with self.network() as ext_net:
            network_id = ext_net['network']['id']
            self._set_net_external(network_id)
            with self.subnet(ext_net, cidr='**********/24'), \
                    self.router() as router, \
                    self.subnet(cidr='********/24') as private_subnet, \
                    self.port(private_subnet) as port:
                self._add_external_gateway_to_router(
                    router['router']['id'],
                    network_id)
                self._router_interface_action(
                    'add', router['router']['id'],
                    private_subnet['subnet']['id'],
                    None)
                fip1 = self._make_floatingip(
                    self.fmt,
                    network_id)
                self.assertIsNone(fip1['floatingip'].get('port_id'))
                port_ip_address = port['port']['fixed_ips'][0]['ip_address']
                port_id = port['port']['id']
                floatingip_id = fip1['floatingip']['id']
                pf_created = self._create_fip_port_forwarding(
                    self.fmt, floatingip_id,
                    2222,
                    22,
                    'tcp',
                    port_ip_address,
                    port_id)
                self.assertEqual(exc.HTTPCreated.code, pf_created.status_int)
                # 1.Test to create pf with value [udp,22,2222]
                pf_udp_created = self._create_fip_port_forwarding(
                    self.fmt,
                    floatingip_id,
                    2222,
                    22,
                    'udp',
                    port_ip_address,
                    port_id)
                self.assertLessEqual(exc.HTTPCreated.code,
                                     pf_udp_created.status_int)
                # 2.Test to create pf with duplicate internal and external port
                pf_dup_res1 = self._create_fip_port_forwarding(
                    self.fmt,
                    floatingip_id,
                    None,
                    None,
                    'tcp',
                    port_ip_address,
                    port_id,
                    internal_port_range="22:22",
                    external_port_range="2222:2222")
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_dup_res1.status_int)
                # 3.Test to create pf with duplicate internal port range
                pf_dup_res2 = self._create_fip_port_forwarding(
                    self.fmt,
                    floatingip_id,
                    None,
                    None,
                    'tcp',
                    port_ip_address,
                    port_id,
                    internal_port_range="22:22",
                    external_port_range="2223:2223")
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_dup_res2.status_int)
                # 4.Test to create pf with duplicate external port range
                tcp_dup_res3 = self._create_fip_port_forwarding(
                    self.fmt,
                    floatingip_id,
                    None,
                    None,
                    'tcp',
                    port_ip_address,
                    port_id,
                    internal_port_range="23:23",
                    external_port_range="2222:2222")
                self.assertEqual(exc.HTTPBadRequest.code,
                                 tcp_dup_res3.status_int)
                # 5.Test to create pf with duplicate internal and external
                # ranges
                pf_dup_res4 = self._create_fip_port_forwarding(
                    self.fmt,
                    floatingip_id,
                    None,
                    None,
                    'tcp',
                    port_ip_address,
                    port_id,
                    internal_port_range="20:22",
                    external_port_range="2220:2222")
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_dup_res4.status_int)
                # 6.Test to create pf with duplicate tcp internal and external
                # ranges
                pf_dup_res5 = self._create_fip_port_forwarding(
                    self.fmt,
                    floatingip_id,
                    None,
                    None,
                    'udp',
                    port_ip_address,
                    port_id,
                    internal_port_range="20:22",
                    external_port_range="2220:2222")
                self.assertEqual(
                    exc.HTTPBadRequest.code, pf_dup_res5.status_int)
                # 7.Test to create pf with [tcp,20,2220]
                pf_created3 = self._create_fip_port_forwarding(
                    self.fmt,
                    floatingip_id,
                    None,
                    None,
                    'tcp',
                    port_ip_address,
                    port_id,
                    internal_port_range="20:20",
                    external_port_range="2220:2220")
                self.assertEqual(exc.HTTPCreated.code, pf_created3.status_int)
                # 8.Test to create pf with [tcp,21,2221]
                pf_created4 = self._create_fip_port_forwarding(
                    self.fmt,
                    floatingip_id,
                    2221,
                    21,
                    'tcp',
                    port_ip_address,
                    port_id)
                self.assertEqual(exc.HTTPCreated.code, pf_created4.status_int)
                # 9.Test to create pf with duplicate internal and external
                # ranges
                pf_dup_res9 = self._create_fip_port_forwarding(
                    self.fmt,
                    floatingip_id,
                    None,
                    None,
                    'tcp',
                    port_ip_address,
                    port_id,
                    external_port_range="2220:2230",
                    internal_port_range="20:30")
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_dup_res9.status_int)
                # 10.Test to create pf with [tcp, 40:50, 2240:2250]
                pf_created5 = self._create_fip_port_forwarding(
                    self.fmt,
                    floatingip_id,
                    None,
                    None,
                    'tcp',
                    port_ip_address,
                    port_id,
                    external_port_range="2240:2250",
                    internal_port_range="40:50")
                self.assertEqual(exc.HTTPCreated.code, pf_created5.status_int)
                # 11.Test to create pf with duplicate internal port ranges
                pf_dup_res11 = self._create_fip_port_forwarding(
                    self.fmt,
                    floatingip_id,
                    None,
                    None,
                    'tcp',
                    port_ip_address,
                    port_id,
                    external_port_range="2210:2310",
                    internal_port_range="10:110")
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_dup_res11.status_int)

                # Test to update pf resources
                pf_id_updated = pf_created.json['port_forwarding']['id']
                fip2 = self._make_floatingip(self.fmt, network_id)
                floatingip_id_new = fip2['floatingip']['id']
                # 1.Test to update pf with internal port 22
                pf_updated1 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_updated,
                    **{'internal_port': 22})
                self.assertEqual(exc.HTTPOk.code, pf_updated1.status_int)

                # 2.Test to update pf with external port 2222
                pf_updated2 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_updated, **{'external_port': 2222})
                self.assertEqual(exc.HTTPOk.code, pf_updated2.status_int)

                # 3.Test to update pf with value [tcp, 30:33, 3330:3333]
                pf_updated3 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_updated, **{
                        'internal_port_range': '30:33',
                        'external_port_range': '3330:3333',
                        'protocol': 'tcp',
                    }
                )
                self.assertEqual(exc.HTTPOk.code, pf_updated3.status_int)

                # 4.Test to update pf with value [udp, 30, 3330, fip2]
                pf_updated4 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_updated, **{
                        'internal_port_range': '30:33',
                        'external_port_range': '3330:3333',
                        'protocol': 'udp',
                    })
                self.assertEqual(exc.HTTPOk.code, pf_updated4.status_int)

                # 5.Test to update pf with [tcp, 22, 2222, fip_1]
                pf_created_updated1 = self._create_fip_port_forwarding(
                    self.fmt, floatingip_id,
                    2222,
                    22,
                    'tcp',
                    port_ip_address,
                    port_id)
                self.assertEqual(exc.HTTPCreated.code,
                                 pf_created_updated1.status_int)
                pf_updated5 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id,
                    pf_id_updated, **{
                        'internal_port_range': '22:22',
                        'external_port_range': '2222:2222',
                        'protocol': 'tcp',
                    })
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_updated5.status_int)

                # 6.Test to update pf with [tcp, 20:40, 2220:2240, fip_1]
                pf_updated6 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id,
                    pf_id_updated, **{
                        'internal_port_range': '20:40',
                        'external_port_range': '2220:2240',
                        'protocol': 'tcp',
                    })
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_updated6.status_int)

                # 7.Test to update pf with [tcp, 20:40, 3320:3340, fip_2]
                pf_updated7 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_updated, **{
                        'internal_port_range': '20:40',
                        'external_port_range': '2220:2240',
                        'protocol': 'tcp',
                    })
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_updated7.status_int)
                # 8.Test to update pf with [tcp, 10, 2210, fip_1]
                pf_updated8 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id,
                    pf_id_updated, **{
                        'internal_port': 10,
                        'external_port': 2210,
                        'protocol': 'tcp',
                    })
                self.assertEqual(exc.HTTPOk.code, pf_updated8.status_int)

                # 9.Test to update pf with [tcp, 10:100, 2210:2300, fip_1]
                pf_updated9 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id,
                    pf_id_updated, **{
                        'internal_port_range': "10:100",
                        'external_port_range': "2210:2300",
                        'protocol': 'tcp',
                    })
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_updated9.status_int)

                # 10.Test to update pf with [tcp, 3000:3000, 4000:4000]
                pf_created_4000 = self._create_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    4000,
                    4000,
                    'tcp',
                    port_ip_address,
                    port_id)
                self.assertEqual(exc.HTTPCreated.code,
                                 pf_created_4000.status_int)
                pf_id_4000 = pf_created_4000.json['port_forwarding']['id']
                pf_updated10 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_4000, **{
                        'internal_port_range': '3000:3000',
                        'external_port_range': '4000:4000',
                        'protocol': 'tcp',
                    })
                self.assertEqual(exc.HTTPOk.code, pf_updated10.status_int)

                # 11.Test to update pf with [tcp, 10:30, 4010:4030]
                pf_updated11 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_4000, **{
                        'internal_port_range': '10:30',
                        'external_port_range': '4010:4030',
                        'protocol': 'tcp',
                    })
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_updated11.status_int)

                # 12.Test to update pf with conflict internal port ranges
                pf_updated12 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_updated, **{
                        'internal_port_range': "10:30",
                        'external_port_range': "2210:2230",
                        'protocol': 'tcp',
                    })
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_updated12.status_int)

                # 13.Test to update pf with duplicate external port ranges
                pf_updated13 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_updated, **{
                        'internal_port_range': "110:130",
                        'external_port_range': "4010:4030",
                        'protocol': 'tcp',
                    })
                self.assertEqual(exc.HTTPOk.code,
                                 pf_updated13.status_int)

                # 14.Test to update pf with all internal and external conflict
                # port ranges
                pf_updated14 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_updated, **{
                        'internal_port_range': "10:100",
                        'external_port_range': "4010:4100",
                        'protocol': 'tcp',
                    })
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_updated14.status_int)

                # 15.Test to update pf with conflict value
                # [udp 10:100, 4010:4100], internal port range is overlapped.
                pf_updated15 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_updated, **{
                        'internal_port_range': "10:100",
                        'external_port_range': "4010:4100",
                        'protocol': 'udp',
                    })
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_updated15.status_int)

                # 16.Test to update pf with no conflict value
                # [udp 6000:7000, 3900:4900]
                pf_updated16 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_updated, **{
                        'internal_port_range': "6000:7000",
                        'external_port_range': "3900:4900",
                        'protocol': 'udp',
                    })
                self.assertEqual(exc.HTTPOk.code,
                                 pf_updated16.status_int)

                # 17.Test to update pf with conflict value
                # [tcp 6000:7000, 3900:4900], external port range is
                # overlapped
                pf_updated17 = self._update_fip_port_forwarding(
                    self.fmt, floatingip_id_new,
                    pf_id_updated, **{
                        'internal_port_range': "6000:7000",
                        'external_port_range': "3900:4900",
                        'protocol': 'tcp',
                    })
                self.assertEqual(exc.HTTPBadRequest.code,
                                 pf_updated17.status_int)
