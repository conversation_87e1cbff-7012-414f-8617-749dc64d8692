# Copyright 2020 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add floatingip id to metering labels

Revision ID: f5eb548334bc
Revises: d6083e887554
Create Date: 2020-06-06 11:34:27.282257

"""

from alembic import op
from neutron_lib.db import constants as db_const
import sqlalchemy as sa

METER_UUID_SIZE = db_const.UUID_FIELD_SIZE
METER_TRIGGERTYPE_SIZE = db_const.UUID_FIELD_SIZE
METER_IP_VER_SIZE = db_const.STATUS_FIELD_SIZE

# revision identifiers, used by Alembic.
revision = 'f5eb548334bc'
down_revision = 'd6083e887554'


def upgrade():
    table_name = 'meteringlabels'
    index_name = 'ix_meteringlabels_floatingip_id'
    existColumn = 0b0
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for column in insp.get_columns(table_name):
        existColumn |= 0b1 if column['name'] == 'floatingip_id' else 0b0
        existColumn |= 0b10 if column['name'] == 'triggeredtype' else 0b00
        existColumn |= 0b100 if column['name'] == 'ipversion' else 0b000

    if not existColumn & 0b1:
        op.add_column(
            'meteringlabels',
            sa.Column(
                'floatingip_id',
                sa.String(
                    length=METER_UUID_SIZE),
                nullable=False)),
    if not existColumn & 0b10:
        op.add_column(
            'meteringlabels',
            sa.Column(
                'triggeredtype',
                sa.String(
                    length=METER_TRIGGERTYPE_SIZE),
                nullable=True)),
    if not existColumn & 0b100:
        op.add_column(
            'meteringlabels',
            sa.Column(
                'ipversion',
                sa.String(
                    length=METER_IP_VER_SIZE),
                nullable=True))

    if index_name not in [idx['name'] for idx in
                          insp.get_indexes(table_name)]:
        op.create_index(
            index_name,
            table_name,
            ['floatingip_id'],
            unique=False)
