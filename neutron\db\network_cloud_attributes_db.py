# Copyright 2021 OpenStack Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.
#

from neutron_lib.callbacks import events
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources

from neutron.common import utils
from neutron.db.models.network_cloud_attributes import NetworkCloudAttributes
from neutron.extensions.cloud_attribute import validate_cloud_attributes


@registry.has_registry_receivers
class NetworkCloudAttributesMixin(object):
    """Mixin class of network's cloud_attributes."""

    def add_cloud_attributes_value_to_database(self, context, network,
                                               cloud_attributes):
        network_id = network.get('id')
        new = NetworkCloudAttributes(network_id=network_id,
                                     cloud_attributes=cloud_attributes)
        if not context.session.is_active:
            raise RuntimeError("add_cloud_attributes_value_to_database"
                               "cannot be called out of a transaction.")
        context.session.add(new)

    @registry.receives(resources.NETWORK, [events.PRECOMMIT_CREATE])
    def _process_cloud_attributes_create(self, resource, event, trigger,
                                         context, network, request, **kwargs):
        cloud_attributes = request.get('cloud_attributes', {})
        validate_cloud_attributes(cloud_attributes)
        cloud_attributes = utils.filter_to_json_str(cloud_attributes)
        self.add_cloud_attributes_value_to_database(
            context, network, cloud_attributes)

    @registry.receives(resources.NETWORK, [events.PRECOMMIT_UPDATE])
    def _process_cloud_attributes_update(self, resource, event, trigger,
                                         payload=None):
        if not payload:
            return
        cloud_attributes = payload.request_body.get('cloud_attributes')
        if not cloud_attributes:
            return
        validate_cloud_attributes(cloud_attributes)
        cur_cloud_attributes = payload.states[0].get('cloud_attributes')
        cur_cloud_attributes.update(cloud_attributes)
        validate_cloud_attributes(cur_cloud_attributes)
        network_db = self._get_network(payload.context,
                                       payload.desired_state['id'])
        payload.desired_state['cloud_attributes'] = cur_cloud_attributes
        cur_cloud_attributes = utils.filter_to_json_str(cur_cloud_attributes)
        if not network_db['cloud_attributes']:
            self.add_cloud_attributes_value_to_database(
                payload.context, payload.desired_state, cur_cloud_attributes)
        else:
            network_db['cloud_attributes'].update(
                {'cloud_attributes': cur_cloud_attributes})
