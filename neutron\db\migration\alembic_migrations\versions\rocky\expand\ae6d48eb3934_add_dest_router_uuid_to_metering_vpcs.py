# Copyright 2020 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add dest router uuid to metering vpcs

Revision ID: ae6d48eb3934
Revises: 8bd5e6822956
Create Date: 2020-06-17 09:02:58.733102

"""

from alembic import op
from neutron_lib.db import constants as db_const
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'ae6d48eb3934'
down_revision = '8bd5e6822956'


def upgrade():
    table_name = 'meteringvpcs'
    existColumn = 0b0
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for column in insp.get_columns(table_name):
        existColumn |= 0b1 if column['name'] == 'dest_router_uuid' else 0b0

    if not existColumn & 0b1:
        op.add_column(
            'meteringvpcs',
            sa.Column(
                'dest_router_uuid',
                sa.String(
                    length=db_const.UUID_FIELD_SIZE),
                nullable=False))
