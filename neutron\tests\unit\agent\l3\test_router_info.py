#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock
from neutron_lib import constants as lib_constants
from oslo_utils import uuidutils

from neutron.agent.l3 import router_info as router_info
from neutron.agent.linux import ip_lib
from neutron.common import exceptions as n_exc
from neutron.conf.agent import common as config
from neutron.conf.agent.l3 import config as l3_config
from neutron.conf.agent.metadata import config as meta_conf
from neutron.tests import base


_uuid = uuidutils.generate_uuid


class TestRouterInfo(base.BaseTestCase):
    def setUp(self):
        super(TestRouterInfo, self).setUp()

        conf = config.setup_conf()
        l3_config.register_l3_agent_config_opts(l3_config.OPTS, conf)
        meta_conf.register_meta_conf_opts(meta_conf.SHARED_OPTS, conf)

        self.ip_cls_p = mock.patch('neutron.agent.linux.ip_lib.IPWrapper')
        ip_cls = self.ip_cls_p.start()
        self.mock_ip = mock.MagicMock()
        ip_cls.return_value = self.mock_ip
        self.ri_kwargs = {'agent_conf': conf}

    def _check_agent_method_called(self, calls):
        self.mock_ip.netns.execute.assert_has_calls(
            [mock.call(call, log_fail_as_error=False) for call in calls],
            any_order=True)

    def test_routing_table_update(self):
        ri = router_info.RouterInfo(
            mock.Mock(), _uuid(), {}, **self.ri_kwargs)
        ri.router = {}

        fake_route1 = {'destination': '***********/16',
                       'nexthop': '*******'}
        fake_route2 = {'destination': '***************/32',
                       'nexthop': '*******'}

        ri.update_routing_table('replace', fake_route1)
        expected = [['ip', 'route', 'replace', 'to', '***********/16',
                     'via', '*******']]
        self._check_agent_method_called(expected)

        ri.update_routing_table('delete', fake_route1)
        expected = [['ip', 'route', 'delete', 'to', '***********/16',
                     'via', '*******']]
        self._check_agent_method_called(expected)

        ri.update_routing_table('replace', fake_route2)
        expected = [['ip', 'route', 'replace', 'to', '***************/32',
                     'via', '*******']]
        self._check_agent_method_called(expected)

        ri.update_routing_table('delete', fake_route2)
        expected = [['ip', 'route', 'delete', 'to', '***************/32',
                     'via', '*******']]
        self._check_agent_method_called(expected)

    def test_update_routing_table(self):
        # Just verify the correct namespace was used in the call
        uuid = _uuid()
        netns = 'qrouter-' + uuid
        fake_route1 = {'destination': '***********/16',
                       'nexthop': '*******'}

        ri = router_info.RouterInfo(mock.Mock(), uuid,
                                    {'id': uuid}, **self.ri_kwargs)
        ri._update_routing_table = mock.Mock()

        ri.update_routing_table('replace', fake_route1)
        ri._update_routing_table.assert_called_once_with('replace',
                                                         fake_route1,
                                                         netns)

    def test_routes_updated(self):
        ri = router_info.RouterInfo(
            mock.Mock(), _uuid(), {}, **self.ri_kwargs)
        ri.router = {}

        fake_old_routes = []
        fake_new_routes = [{'destination': "************/24",
                            'nexthop': "************"},
                           {'destination': "************/24",
                            'nexthop': "************"}]
        ri.routes = fake_old_routes
        ri.router['routes'] = fake_new_routes
        ri.routes_updated(fake_old_routes, fake_new_routes)

        expected = [['ip', 'route', 'replace', 'to', '************/24',
                    'via', '************'],
                    ['ip', 'route', 'replace', 'to', '************/24',
                     'via', '************']]

        self._check_agent_method_called(expected)
        ri.routes = fake_new_routes
        fake_new_routes = [{'destination': "************/24",
                            'nexthop': "************"}]
        ri.router['routes'] = fake_new_routes
        ri.routes_updated(ri.routes, fake_new_routes)
        expected = [['ip', 'route', 'delete', 'to', '************/24',
                    'via', '************']]

        self._check_agent_method_called(expected)
        fake_new_routes = []
        ri.router['routes'] = fake_new_routes
        ri.routes_updated(ri.routes, fake_new_routes)

        expected = [['ip', 'route', 'delete', 'to', '************/24',
                    'via', '************']]
        self._check_agent_method_called(expected)

    def test__process_pd_iptables_rules(self):
        subnet_id = _uuid()
        ex_gw_port = {'id': _uuid()}
        prefix = '2001:db8:cafe::/64'

        ri = router_info.RouterInfo(
            mock.Mock(), _uuid(), {}, **self.ri_kwargs)

        ipv6_mangle = ri.iptables_manager.ipv6['mangle'] = mock.MagicMock()
        ri.get_ex_gw_port = mock.Mock(return_value=ex_gw_port)
        ri.get_external_device_name = mock.Mock(return_value='fake_device')
        ri.get_address_scope_mark_mask = mock.Mock(return_value='fake_mark')

        ri._process_pd_iptables_rules(prefix, subnet_id)

        mangle_rule = '-d %s ' % prefix
        mangle_rule += ri.address_scope_mangle_rule('fake_device', 'fake_mark')

        ipv6_mangle.add_rule.assert_called_once_with(
            'scope',
            mangle_rule,
            tag='prefix_delegation_%s' % subnet_id)

    def test_add_ports_address_scope_iptables(self):
        ri = router_info.RouterInfo(
            mock.Mock(), _uuid(), {}, **self.ri_kwargs)
        port = {
            'id': _uuid(),
            'fixed_ips': [{'ip_address': '*********'}],
            'address_scopes': {lib_constants.IP_VERSION_4: '1234'}
        }
        ipv4_mangle = ri.iptables_manager.ipv4['mangle'] = mock.MagicMock()
        ri.get_address_scope_mark_mask = mock.Mock(return_value='fake_mark')
        ri.get_internal_device_name = mock.Mock(return_value='fake_device')
        ri.rt_tables_manager = mock.MagicMock()
        ri.process_external_port_address_scope_routing = mock.Mock()
        ri.process_floating_ip_address_scope_rules = mock.Mock()
        ri.iptables_manager._apply = mock.Mock()

        ri.router[lib_constants.INTERFACE_KEY] = [port]
        ri.process_address_scope()

        ipv4_mangle.add_rule.assert_called_once_with(
            'scope', ri.address_scope_mangle_rule('fake_device', 'fake_mark'))

    def test_address_scope_mark_ids_handling(self):
        mark_ids = set(range(router_info.ADDRESS_SCOPE_MARK_ID_MIN,
                             router_info.ADDRESS_SCOPE_MARK_ID_MAX))
        ri = router_info.RouterInfo(
            mock.Mock(), _uuid(), {}, **self.ri_kwargs)
        # first mark id is used for the default address scope
        scope_to_mark_id = {router_info.DEFAULT_ADDRESS_SCOPE:
                            mark_ids.pop()}
        self.assertEqual(scope_to_mark_id, ri._address_scope_to_mark_id)
        self.assertEqual(mark_ids, ri.available_mark_ids)

        # new id should be used for new address scope
        ri.get_address_scope_mark_mask('new_scope')
        scope_to_mark_id['new_scope'] = mark_ids.pop()
        self.assertEqual(scope_to_mark_id, ri._address_scope_to_mark_id)
        self.assertEqual(mark_ids, ri.available_mark_ids)

        # new router should have it's own mark ids set
        new_mark_ids = set(range(router_info.ADDRESS_SCOPE_MARK_ID_MIN,
                                 router_info.ADDRESS_SCOPE_MARK_ID_MAX))
        new_ri = router_info.RouterInfo(mock.Mock(), _uuid(),
                                        {}, **self.ri_kwargs)
        new_mark_ids.pop()
        self.assertEqual(new_mark_ids, new_ri.available_mark_ids)
        self.assertNotEqual(ri.available_mark_ids, new_ri.available_mark_ids)

    def test_process_delete(self):
        ri = router_info.RouterInfo(
            mock.Mock(), _uuid(), {}, **self.ri_kwargs)
        ri.router = {'id': _uuid()}
        with mock.patch.object(ri, '_process_internal_ports') as p_i_p,\
            mock.patch.object(ri, '_process_external_on_delete') as p_e_o_d:
            self.mock_ip.netns.exists.return_value = False
            ri.process_delete()
            self.assertFalse(p_i_p.called)
            self.assertFalse(p_e_o_d.called)

            p_i_p.reset_mock()
            p_e_o_d.reset_mock()
            self.mock_ip.netns.exists.return_value = True
            ri.process_delete()
            p_i_p.assert_called_once_with()
            p_e_o_d.assert_called_once_with()

    def test__update_internal_ports_cache(self):
        ri = router_info.RouterInfo(
            mock.Mock(), _uuid(), {}, **self.ri_kwargs)
        ri.internal_ports = [
            {'id': 'port-id-1', 'mtu': 1500},
            {'id': 'port-id-2', 'mtu': 2000}]
        initial_internal_ports = ri.internal_ports[:]

        # Test add new element to the cache
        new_port = {'id': 'new-port-id', 'mtu': 1500}
        ri._update_internal_ports_cache(new_port)
        self.assertEqual(
            initial_internal_ports + [new_port],
            ri.internal_ports)

        # Test update existing port in cache
        updated_port = new_port.copy()
        updated_port['mtu'] = 2500
        ri._update_internal_ports_cache(updated_port)
        self.assertEqual(
            initial_internal_ports + [updated_port],
            ri.internal_ports)


class BasicRouterTestCaseFramework(base.BaseTestCase):
    def _create_router(self, router=None, **kwargs):
        if not router:
            router = mock.MagicMock()
        self.agent_conf = mock.Mock()
        self.router_id = _uuid()
        return router_info.RouterInfo(mock.Mock(),
                                      self.router_id,
                                      router,
                                      self.agent_conf,
                                      mock.sentinel.interface_driver,
                                      **kwargs)


class TestBasicRouterOperations(BasicRouterTestCaseFramework):

    def test_get_floating_ips(self):
        router = mock.MagicMock()
        router.get.return_value = [mock.sentinel.floating_ip]
        ri = self._create_router(router)

        fips = ri.get_floating_ips()

        self.assertEqual([mock.sentinel.floating_ip], fips)

    def test_get_pf_floating_ips(self):
        router = mock.MagicMock()
        router.get.return_value = [mock.sentinel.floating_ip]
        ri = self._create_router(router)

        pf_fips = ri.get_port_forwarding_fips()

        self.assertEqual([mock.sentinel.floating_ip], pf_fips)

    def test_process_floating_ip_nat_rules(self):
        ri = self._create_router()
        fips = [{'fixed_ip_address': mock.sentinel.ip,
                 'fip_type': 'floatingip',
                 'floating_ip_address': '*******'}]
        ri.get_floating_ips = mock.Mock(return_value=fips)
        ri.iptables_manager = mock.MagicMock()
        ipv4_nat = ri.iptables_manager.ipv4['nat']
        ri.floating_forward_rules = mock.Mock(
            return_value=[(mock.sentinel.chain, mock.sentinel.rule)])

        ri.process_floating_ip_nat_rules()

        # Be sure that the rules are cleared first and apply is called last
        self.assertEqual(mock.call.clear_rules_by_tag('floating_ip'),
                         ipv4_nat.mock_calls[0])
        self.assertEqual(mock.call.apply(), ri.iptables_manager.mock_calls[-1])

        # Be sure that add_rule is called somewhere in the middle
        ipv4_nat.add_rule.assert_called_once_with(mock.sentinel.chain,
                                                  mock.sentinel.rule,
                                                  tag='floating_ip')

    def test_process_floating_ip_nat_rules_removed(self):
        ri = self._create_router()
        ri.get_floating_ips = mock.Mock(return_value=[])
        ri.iptables_manager = mock.MagicMock()
        ipv4_nat = ri.iptables_manager.ipv4['nat']

        ri.process_floating_ip_nat_rules()

        # Be sure that the rules are cleared first and apply is called last
        self.assertEqual(mock.call.clear_rules_by_tag('floating_ip'),
                         ipv4_nat.mock_calls[0])
        self.assertEqual(mock.call.apply(), ri.iptables_manager.mock_calls[-1])

        # Be sure that add_rule is called somewhere in the middle
        self.assertFalse(ipv4_nat.add_rule.called)

    def test_process_floating_ip_address_scope_rules_diff_scopes(self):
        ri = self._create_router()
        fips = [{'fixed_ip_address': mock.sentinel.ip,
                 'fip_type': 'floatingip',
                 'floating_ip_address': '*******',
                 'fixed_ip_address_scope': 'scope1'}]
        ri.get_floating_ips = mock.Mock(return_value=fips)
        ri._get_external_address_scope = mock.Mock(return_value='scope2')
        ipv4_mangle = ri.iptables_manager.ipv4['mangle'] = mock.MagicMock()
        ri.floating_mangle_rules = mock.Mock(
            return_value=[(mock.sentinel.chain1, mock.sentinel.rule1)])
        ri.get_external_device_name = mock.Mock()

        ri.process_floating_ip_address_scope_rules()

        # Be sure that the rules are cleared first
        self.assertEqual(mock.call.clear_rules_by_tag('floating_ip'),
                         ipv4_mangle.mock_calls[0])
        # Be sure that add_rule is called somewhere in the middle
        self.assertEqual(1, ipv4_mangle.add_rule.call_count)
        self.assertEqual(mock.call.add_rule(mock.sentinel.chain1,
                                            mock.sentinel.rule1,
                                            tag='floating_ip'),
                         ipv4_mangle.mock_calls[1])

    def test_process_floating_ip_address_scope_rules_same_scopes(self):
        ri = self._create_router()
        fips = [{'fixed_ip_address': mock.sentinel.ip,
                 'floating_ip_address': mock.sentinel.fip,
                 'fixed_ip_address_scope': 'scope1'}]
        ri.get_floating_ips = mock.Mock(return_value=fips)
        ri._get_external_address_scope = mock.Mock(return_value='scope1')
        ipv4_mangle = ri.iptables_manager.ipv4['mangle'] = mock.MagicMock()

        ri.process_floating_ip_address_scope_rules()

        # Be sure that the rules are cleared first
        self.assertEqual(mock.call.clear_rules_by_tag('floating_ip'),
                         ipv4_mangle.mock_calls[0])
        # Be sure that add_rule is not called somewhere in the middle
        self.assertFalse(ipv4_mangle.add_rule.called)

    def test_process_floating_ip_mangle_rules_removed(self):
        ri = self._create_router()
        ri.get_floating_ips = mock.Mock(return_value=[])
        ipv4_mangle = ri.iptables_manager.ipv4['mangle'] = mock.MagicMock()

        ri.process_floating_ip_address_scope_rules()

        # Be sure that the rules are cleared first
        self.assertEqual(mock.call.clear_rules_by_tag('floating_ip'),
                         ipv4_mangle.mock_calls[0])

        # Be sure that add_rule is not called somewhere in the middle
        self.assertFalse(ipv4_mangle.add_rule.called)

    def test_process_fip_admin_state_up(self):
        ri = self._create_router()
        fips = [{'fixed_ip_address': '*******',
                 'fip_type': 'floatingip',
                 'floating_ip_address': '*******',
                 'admin_state_up': False}]
        ri.get_floating_ips = mock.Mock(return_value=fips)
        ri.get_port_forwarding_fips = mock.Mock(return_value=[])
        ri.get_elastic_snat_fips = mock.Mock(return_value=[])
        ri.iptables_manager = mock.MagicMock()
        ipv4_raw = ri.iptables_manager.ipv4['raw']
        ipv4_filter = ri.iptables_manager.ipv4['filter']
        ri.get_external_device_interface_name = mock.Mock(
            return_value='qg-ca301cc2-48')
        ri.process_floating_ip_address_fip_admin_rules()
        ipv4_fip_admin_ingress_rule = '-d *******/32 -j DROP'
        ipv4_fip_admin_egress_rule = (
                '-o qg-ca301cc2-48 -s *******/32 -j DROP')
        # Be sure that the rules are cleared first and apply is called last
        self.assertEqual(mock.call.empty_chain('fip-admin'),
                         ipv4_raw.mock_calls[0])
        self.assertEqual(mock.call.empty_chain('fip-admin'),
                         ipv4_filter.mock_calls[1])
        self.assertEqual(mock.call.add_rule('fip-admin',
                                            ipv4_fip_admin_ingress_rule,
                                            tag='floatingip_admin'),
                         ipv4_raw.mock_calls[2])
        self.assertEqual(mock.call.add_rule('fip-admin',
                                            ipv4_fip_admin_egress_rule,
                                            tag='floatingip_admin'),
                         ipv4_filter.mock_calls[3])

    def test_process_pf_fip_admin_state_up(self):
        ri = self._create_router()
        fips = [{'fixed_ip_address': None,
                 'fip_type': 'floatingip',
                 'floating_ip_address': '*******',
                 'admin_state_up': False}]
        ri.get_floating_ips = mock.Mock(return_value=[])
        ri.get_port_forwarding_fips = mock.Mock(return_value=fips)
        ri.get_elastic_snat_fips = mock.Mock(return_value=[])
        ri.iptables_manager = mock.MagicMock()
        ipv4_raw = ri.iptables_manager.ipv4['raw']
        ri.get_external_device_name = mock.Mock()
        ri.process_floating_ip_address_fip_admin_rules()
        ipv4_fip_admin_ingress_rule = '-d *******/32 -j DROP'
        # Be sure that the rules are cleared first and apply is called last
        self.assertEqual(mock.call.empty_chain('fip-admin'),
                         ipv4_raw.mock_calls[0])
        self.assertEqual(mock.call.add_rule('fip-admin',
                                            ipv4_fip_admin_ingress_rule,
                                            tag='floatingip_admin'),
                         ipv4_raw.mock_calls[2])

    def test_process_ipv6_admin_state_up(self):
        ri = self._create_router()
        fips = [{'fixed_ip_address': None,
                 'fip_type': 'ecs_ipv6',
                 'floating_ip_address': '2202:db8:cafe:1::17a',
                 'admin_state_up': False}]
        ri.get_floating_ips = mock.Mock(return_value=fips)
        ri.get_port_forwarding_fips = mock.Mock(return_value=[])
        ri.get_elastic_snat_fips = mock.Mock(return_value=[])
        ri.agent_conf.enable_nat6to6 = False
        ri.iptables_manager = mock.MagicMock()
        ipv6_filter = ri.iptables_manager.ipv6['filter']
        ri.ipv6_fip_admin_filter_rule = mock.Mock(
            return_value=[mock.sentinel.rule3])
        ri.get_external_device_name = mock.Mock()

        ri.process_floating_ip_address_fip_admin_rules()

        # Be sure that the rules are cleared first and apply is called last
        self.assertEqual(mock.call.empty_chain('fip-admin'),
                         ipv6_filter.mock_calls[0])
        self.assertEqual(mock.call.add_rule('fip-admin',
                                            mock.sentinel.rule3,
                                            tag='floatingip_admin'),
                         ipv6_filter.mock_calls[1])

    def test_process_fip_denied_ports(self):
        ri = self._create_router()
        fips = [{'fixed_ip_address': mock.sentinel.ip,
                 'fip_type': 'floatingip',
                 'floating_ip_address': '*******',
                 'allowed_port_numbers': ['80'],
                 'denied_port_numbers': ['8080', '443']}]
        ri.get_floating_ips = mock.Mock(return_value=fips)
        ri.get_port_forwarding_fips = mock.Mock(return_value=[])
        ri.get_elastic_snat_fips = mock.Mock(return_value=[])
        ri.iptables_manager = mock.MagicMock()
        ipv4_raw = ri.iptables_manager.ipv4['raw']
        ri.port_num_ipv4_rule = mock.Mock(
            return_value=[mock.sentinel.rule2])
        ri.get_external_device_name = mock.Mock()

        ri.process_fip_denied_ports_rules()

        # Be sure that the rules are cleared first and apply is called last
        self.assertEqual(mock.call.empty_chain('denied-port'),
                         ipv4_raw.mock_calls[0])
        self.assertEqual(mock.call.add_rule('denied-port',
                                            mock.sentinel.rule2,
                                            tag='denied-port'),
                         ipv4_raw.mock_calls[1])

    def test_process_pf_fip_denied_ports(self):
        ri = self._create_router()
        fips = [{'fixed_ip_address': None,
                 'fip_type': 'floatingip',
                 'floating_ip_address': '*******',
                 'allowed_port_numbers': ['80'],
                 'denied_port_numbers': ['8080', '443']}]
        ri.get_floating_ips = mock.Mock(return_value=[])
        ri.get_port_forwarding_fips = mock.Mock(return_value=fips)
        ri.get_elastic_snat_fips = mock.Mock(return_value=[])
        ri.iptables_manager = mock.MagicMock()
        ipv4_raw = ri.iptables_manager.ipv4['raw']
        ri.port_num_ipv4_rule = mock.Mock(
            return_value=[mock.sentinel.rule2])
        ri.get_external_device_name = mock.Mock()

        ri.process_fip_denied_ports_rules()

        # Be sure that the rules are cleared first and apply is called last
        self.assertEqual(mock.call.empty_chain('denied-port'),
                         ipv4_raw.mock_calls[0])
        self.assertEqual(mock.call.add_rule('denied-port',
                                            mock.sentinel.rule2,
                                            tag='denied-port'),
                         ipv4_raw.mock_calls[1])

    def test_process_es_fip_denied_ports(self):
        ri = self._create_router()
        fips = [{'fixed_ip_address': None,
                 'fip_type': 'floatingip',
                 'floating_ip_address': '*******',
                 'allowed_port_numbers': ['80'],
                 'denied_port_numbers': ['8080', '443']}]
        ri.get_floating_ips = mock.Mock(return_value=[])
        ri.get_port_forwarding_fips = mock.Mock(return_value=[])
        ri.get_elastic_snat_fips = mock.Mock(return_value=fips)
        ri.iptables_manager = mock.MagicMock()
        ipv4_raw = ri.iptables_manager.ipv4['raw']
        ri.port_num_ipv4_rule = mock.Mock(
            return_value=[mock.sentinel.rule2])
        ri.get_external_device_name = mock.Mock()

        ri.process_fip_denied_ports_rules()

        # Be sure that the rules are cleared first and apply is called last
        self.assertEqual(mock.call.empty_chain('denied-port'),
                         ipv4_raw.mock_calls[0])
        self.assertEqual(mock.call.add_rule('denied-port',
                                            mock.sentinel.rule2,
                                            tag='denied-port'),
                         ipv4_raw.mock_calls[1])

    def test_process_ipv6_denied_ports(self):
        ri = self._create_router()
        fips = [{'fixed_ip_address': None,
                 'fip_type': 'ecs_ipv6',
                 'floating_ip_address': '2202:db8:cafe:1::17a',
                 'admin_state_up': True,
                 'allowed_port_numbers': ['80'],
                 'denied_port_numbers': ['8080', '443']}]
        ri.get_floating_ips = mock.Mock(return_value=fips)
        ri.get_port_forwarding_fips = mock.Mock(return_value=[])
        ri.get_elastic_snat_fips = mock.Mock(return_value=[])
        ri.iptables_manager = mock.MagicMock()
        ri.agent_conf.enable_nat6to6 = False
        ipv6_filter = ri.iptables_manager.ipv6['filter']
        ri.port_num_ipv6_rule = mock.Mock(
            return_value=[mock.sentinel.rule3])
        ri.get_external_device_name = mock.Mock()

        ri.process_fip_denied_ports_rules()

        # Be sure that the rules are cleared first and apply is called last
        self.assertEqual(mock.call.empty_chain('denied-port'),
                         ipv6_filter.mock_calls[0])
        self.assertEqual(mock.call.add_rule('denied-port',
                                            mock.sentinel.rule3,
                                            tag='denied-port'),
                         ipv6_filter.mock_calls[1])

    def _test_add_fip_addr_to_device_error(self, device):
        ri = self._create_router()
        ip = '********'

        result = ri._add_fip_addr_to_device(
            {'id': mock.sentinel.id, 'floating_ip_address': ip}, device)

        device.addr.add.assert_called_with(ip + '/32')
        return result

    def test__add_fip_addr_to_device(self):
        result = self._test_add_fip_addr_to_device_error(mock.Mock())
        self.assertTrue(result)

    def test__add_fip_addr_to_device_error(self):
        device = mock.Mock()
        device.addr.add.side_effect = RuntimeError
        result = self._test_add_fip_addr_to_device_error(device)
        self.assertFalse(result)

    def test_process_snat_dnat_for_fip(self):
        ri = self._create_router()
        ri.process_floating_ip_nat_rules = mock.Mock(side_effect=Exception)

        self.assertRaises(n_exc.FloatingIpSetupException,
                          ri.process_snat_dnat_for_fip)

        ri.process_floating_ip_nat_rules.assert_called_once_with()

    def test_put_fips_in_error_state(self):
        ri = self._create_router()
        ri.router = mock.Mock()
        ri.router.get.return_value = [{'id': mock.sentinel.id1},
                                      {'id': mock.sentinel.id2}]

        statuses = ri.put_fips_in_error_state()

        expected = [{mock.sentinel.id1: lib_constants.FLOATINGIP_STATUS_ERROR,
                     mock.sentinel.id2: lib_constants.FLOATINGIP_STATUS_ERROR}]
        self.assertNotEqual(expected, statuses)

    def test_configure_fip_addresses(self):
        ri = self._create_router()
        ri.process_floating_ip_addresses = mock.Mock(
            side_effect=Exception)

        self.assertRaises(n_exc.FloatingIpSetupException,
                          ri.configure_fip_addresses,
                          mock.sentinel.interface_name)

        ri.process_floating_ip_addresses.assert_called_once_with(
            mock.sentinel.interface_name)

    def test_get_router_cidrs_returns_cidrs(self):
        ri = self._create_router()
        addresses = ['********/24', '********/32']
        device = mock.MagicMock()
        device.addr.list.return_value = [{'cidr': addresses[0]},
                                         {'cidr': addresses[1]}]
        self.assertEqual(set(addresses), ri.get_router_cidrs(device))


@mock.patch.object(ip_lib, 'IPDevice')
class TestFloatingIpWithMockDevice(BasicRouterTestCaseFramework):

    def test_process_floating_ip_addresses_remap(self, IPDevice):
        fip_id = _uuid()
        fip = {
            'id': fip_id, 'port_id': _uuid(),
            'floating_ip_address': '********',
            'fixed_ip_address': '***********',
            'fip_type': 'floatingip',
            'status': lib_constants.FLOATINGIP_STATUS_DOWN
        }

        IPDevice.return_value = device = mock.Mock()
        device.addr.list.return_value = [{'cidr': '********/32'}]
        ri = self._create_router()
        ri.get_floating_ips = mock.Mock(return_value=[fip])

        fip_statuses = ri.process_floating_ip_addresses(
            mock.sentinel.interface_name)
        self.assertEqual({fip_id: lib_constants.FLOATINGIP_STATUS_ACTIVE},
                         fip_statuses)

        self.assertFalse(device.addr.add.called)
        self.assertFalse(device.addr.delete.called)

    def test_process_router_with_disabled_floating_ip(self, IPDevice):
        fip_id = _uuid()
        fip = {
            'id': fip_id, 'port_id': _uuid(),
            'floating_ip_address': '********',
            'fip_type': 'floatingip',
            'fixed_ip_address': '***********'
        }

        ri = self._create_router()
        ri.floating_ips = [fip]
        ri.get_floating_ips = mock.Mock(return_value=[])

        fip_statuses = ri.process_floating_ip_addresses(
            mock.sentinel.interface_name)

        self.assertIsNone(fip_statuses.get(fip_id))

    def test_process_router_floating_ip_with_device_add_error(self, IPDevice):
        IPDevice.return_value = device = mock.Mock(side_effect=RuntimeError)
        device.addr.list.return_value = []
        fip_id = _uuid()
        fip = {
            'id': fip_id, 'port_id': _uuid(),
            'floating_ip_address': '********',
            'fixed_ip_address': '***********',
            'fip_type': 'floatingip',
            'status': 'DOWN'
        }
        ri = self._create_router()
        ri.add_floating_ip = mock.Mock(
            return_value=lib_constants.FLOATINGIP_STATUS_ERROR)
        ri.get_floating_ips = mock.Mock(return_value=[fip])

        fip_statuses = ri.process_floating_ip_addresses(
            mock.sentinel.interface_name)

        self.assertEqual({fip_id: lib_constants.FLOATINGIP_STATUS_ERROR},
                         fip_statuses)

    # TODO(mrsmith): refactor for DVR cases
    def test_process_floating_ip_addresses_remove(self, IPDevice):
        IPDevice.return_value = device = mock.Mock()
        device.addr.list.return_value = [{'cidr': '********/32'}]

        ri = self._create_router()
        ri.remove_floating_ip = mock.Mock()
        ri.router.get = mock.Mock(return_value=[])

        fip_statuses = ri.process_floating_ip_addresses(
            mock.sentinel.interface_name)
        self.assertEqual({}, fip_statuses)
        ri.remove_floating_ip.assert_called_once_with(device, '********/32')

    def test_process_floating_ip_reassignment(self, IPDevice):
        IPDevice.return_value = device = mock.Mock()
        device.addr.list.return_value = [{'cidr': '********/32'}]

        fip_id = _uuid()
        fip = {
            'id': fip_id, 'port_id': _uuid(),
            'floating_ip_address': '********',
            'fixed_ip_address': '***********',
            'fip_type': 'floatingip',
            'status': 'DOWN'
        }
        ri = self._create_router()
        ri.get_floating_ips = mock.Mock(return_value=[fip])
        ri.move_floating_ip = mock.Mock()
        ri.fip_map = {'********': '***********'}

        ri.process_floating_ip_addresses(mock.sentinel.interface_name)
        ri.move_floating_ip.assert_called_once_with(fip)

    def test_process_floating_ip_addresses_gw_secondary_ip_not_removed(
            self, IPDevice):
        IPDevice.return_value = device = mock.Mock()
        device.addr.list.return_value = [{'cidr': '*******/16'},
                                         {'cidr': '*******/32'},
                                         {'cidr': '*******/32'},
                                         {'cidr': '*******/32'}]
        ri = self._create_router()

        ri.get_floating_ips = mock.Mock(return_value=[
            {'id': _uuid(),
             'floating_ip_address': '*******',
             'fixed_ip_address': '*************',
             'fip_type': 'floatingip',
             'status': 'DOWN'}])
        ri.add_floating_ip = mock.Mock()
        ri.get_ex_gw_port = mock.Mock(return_value={
            "fixed_ips": [{"ip_address": "*******"},
                          {"ip_address": "*******"}]})
        ri.remove_floating_ip = mock.Mock()

        ri.process_floating_ip_addresses("qg-fake-device")
        ri.remove_floating_ip.assert_called_once_with(device, '*******/32')


class TestCheckRouter(BasicRouterTestCaseFramework):

    def _create_ha_router(self):
        router = {
            'ha': True
        }
        return self._create_router(router)

    def _test_check_router_worker_process(self, ri,
                                          processes, expected_result):
        ri.internal_ports = [
            {'subnets': [{'cidr': '***********/24'}]},
            {'subnets': [{'cidr': '2001:db8::/32'}]}
        ]
        ri.agent_conf = mock.MagicMock()
        ri.backend = mock.MagicMock()
        ri.backend.process_monitor._monitored_processes = processes
        ri.router_namespace.exists = mock.Mock(return_value=True)
        result = ri.check_router_worker_process()
        self.assertEqual(result["status"],
                         expected_result["status"])
        self.assertEqual(
            result["router_worker_process_error"],
            expected_result["router_worker_process_error"])

    def test_check_router_worker_process_all_running(self):
        ri = self._create_ha_router()
        processes = {
            mock.MagicMock(uuid=ri.router_id, service='radvd'):
                mock.MagicMock(active=True),
            mock.MagicMock(uuid=ri.router_id, service='keepalived'):
                mock.MagicMock(active=True),
            mock.MagicMock(uuid=ri.router_id, service='metadata-proxy'):
                mock.MagicMock(active=True),
            mock.MagicMock(uuid=ri.router_id, service='ip_monitor'):
                mock.MagicMock(active=True),
        }
        expected_result = {
            "status": True,
            "router_worker_process_error": []
        }
        self._test_check_router_worker_process(
            ri, processes, expected_result)

    def test_check_router_worker_process_radvd_not_running(self):
        ri = self._create_ha_router()
        processes = {
            mock.MagicMock(uuid=ri.router_id, service='radvd'):
                mock.MagicMock(active=False),
            mock.MagicMock(uuid=ri.router_id, service='keepalived'):
                mock.MagicMock(active=True),
            mock.MagicMock(uuid=ri.router_id, service='metadata-proxy'):
                mock.MagicMock(active=True),
            mock.MagicMock(uuid=ri.router_id, service='ip_monitor'):
                mock.MagicMock(active=True),
        }
        expected_result = {
            "status": False,
            "router_worker_process_error": [
                "radvd process is not running."]
        }
        self._test_check_router_worker_process(
            ri, processes, expected_result)

    def test_check_router_worker_process_keepalived_not_running(self):
        ri = self._create_ha_router()
        processes = {
            mock.MagicMock(uuid=ri.router_id, service='radvd'):
                mock.MagicMock(active=True),
            mock.MagicMock(uuid=ri.router_id, service='keepalived'):
                mock.MagicMock(active=False),
            mock.MagicMock(uuid=ri.router_id, service='metadata-proxy'):
                mock.MagicMock(active=True),
            mock.MagicMock(uuid=ri.router_id, service='ip_monitor'):
                mock.MagicMock(active=True),
        }
        expected_result = {
            "status": False,
            "router_worker_process_error": [
                "keepalived process is not running."]
        }
        self._test_check_router_worker_process(
            ri, processes, expected_result)

    def test_check_router_worker_process_haproxy_not_running(self):
        ri = self._create_ha_router()
        processes = {
            mock.MagicMock(uuid=ri.router_id, service='radvd'):
                mock.MagicMock(active=True),
            mock.MagicMock(uuid=ri.router_id, service='keepalived'):
                mock.MagicMock(active=True),
            mock.MagicMock(uuid=ri.router_id, service='metadata-proxy'):
                mock.MagicMock(active=False),
            mock.MagicMock(uuid=ri.router_id, service='ip_monitor'):
                mock.MagicMock(active=True),
        }
        expected_result = {
            "status": False,
            "router_worker_process_error": [
                "haproxy process is not running."]
        }
        self._test_check_router_worker_process(
            ri, processes, expected_result)

    def test_check_router_worker_process_ip_monitor_not_running(self):
        ri = self._create_ha_router()
        processes = {
            mock.MagicMock(uuid=ri.router_id, service='radvd'):
                mock.MagicMock(active=True),
            mock.MagicMock(uuid=ri.router_id, service='keepalived'):
                mock.MagicMock(active=True),
            mock.MagicMock(uuid=ri.router_id, service='metadata-proxy'):
                mock.MagicMock(active=True),
            mock.MagicMock(uuid=ri.router_id, service='ip_monitor'):
                mock.MagicMock(active=False),
        }
        expected_result = {
            "status": False,
            "router_worker_process_error": [
                "keepalived-state-change process is not running."]
        }
        self._test_check_router_worker_process(
            ri, processes, expected_result)

    def test_check_router_namespace_exist(self):
        ri = self._create_ha_router()
        ri.router_namespace.exists = mock.Mock(return_value=True)
        result = ri.check_router_namespace()
        ri.router_namespace.exists.assert_called()
        expected_result = {'namespace_error': '', 'status': True}
        self.assertEqual(
            result,
            expected_result)

    def test_check_router_namespace_does_not_exist(self):
        ri = self._create_ha_router()
        ri.router_namespace.exists = mock.Mock(return_value=False)
        result = ri.check_router_namespace()
        ri.router_namespace.exists.assert_called()
        expected_result = {
            'namespace_error': 'Router namespace %s does not exist.' %
                               ri.ns_name,
            'status': False}
        self.assertEqual(
            result,
            expected_result)

    def _test_check_router_routes(self, error_route=None,
                                  expected_result=None):
        router = {
            'routes': [{"destination": "***********/24",
                        "nexthop": "************"}],
            'ha': True
        }
        ri = self._create_router(router)
        ipv4_route = []
        define_route = "***********/24 via ************ dev qr-dc8e7e3b-9c"
        default_route = "default via ************** dev qg-3fd3e92d-3e"
        qr_device_route = "***********/24 dev qr-dc8e7e3b-9c proto "\
                          "kernel scope link src ***********"
        qg_device_route = "************/24 dev qg-3fd3e92d-3e proto "\
                          "kernel scope link src **************"
        ha_device_route = ["***********/24 dev ha-bfb187e5-8d proto "
                           "kernel scope link src ************",
                           "*************/18 dev ha-bfb187e5-8d proto "
                           "kernel scope link src **************"]
        route_dict = {'define': define_route, 'default': default_route,
                      'qr-': qr_device_route, 'qg-': qg_device_route,
                      'ha-': ha_device_route}
        if error_route:
            route_dict.pop(error_route)
        for route in route_dict.values():
            if isinstance(route, list):
                ipv4_route.extend(route)
                continue
            ipv4_route.append(route)

        ip_route = {"ipv4": ipv4_route, "ipv6": []}
        ri.get_ns_ip_route = mock.Mock(return_value=ip_route)

        ri.get_vip_addresses = mock.Mock(
            return_value=('***********/24', '************'))
        ri.router_namespace.exists = mock.Mock(return_value=True)
        ha_device = mock.Mock()
        ha_device.name = "ha-bfb187e5-8d"
        qr_device = mock.Mock()
        qr_device.name = "qr-dc8e7e3b-9c"
        qg_device = mock.Mock()
        qg_device.name = "qg-3fd3e92d-3e"
        ri.internal_ports = [{
            'fixed_ips': [{
                'subnet_id': '4140e828-e153-49e7-9899-f7402f1209eb',
                'ip_address': '***********'}],
            'id': 'dc8e7e3b-9c91-4ef6-9535-50b3e652eee3',
            'subnets': [{'gateway_ip': '***********',
                         'cidr': '***********/24',
                         'id': '4140e828-e153-49e7-9899-f7402f1209eb'}]
        }]
        ri.ex_gw_port = {'fixed_ips': [{
            'subnet_id': '22c5cf80-47ee-4ecf-839f-ba529fb2503b',
            'ip_address': '**************'}],
            'id': '3fd3e92d-3e21-4984-8b2f-52db80ca949a',
            'subnets': [{
                'gateway_ip': '**************',
                'cidr': '************/24',
                'id': '22c5cf80-47ee-4ecf-839f-ba529fb2503b'}]}
        ri.ha_port = {'fixed_ips': [{
            'subnet_id': '94cffe43-e52a-4d57-85b6-66f33dde9470',
            'ip_address': '**************'}],
            'id': 'bfb187e5-8d84-4581-bcae-646a9e73cc67',
            'subnets': [{
                'gateway_ip': None,
                'cidr': '*************/18',
                'id': '94cffe43-e52a-4d57-85b6-66f33dde9470'}]}
        with mock.patch.object(ip_lib, 'IPWrapper') as mock_ip_lib:
            ip_device = [ha_device, qr_device, qg_device]
            ip_instance = mock_ip_lib.return_value
            ip_instance.get_devices.return_value = ip_device
            ri.get_ex_gw_port = mock.Mock(return_value=ri.ex_gw_port)
            res = ri.check_router_routes()
            self.assertEqual(res['status'],
                             expected_result['status'])
            self.assertEqual(res['route_error'],
                             expected_result['route_error'])
            self.assertEqual(res['device_route_error'],
                             expected_result['device_route_error'])

    def test_check_router_routes_ipv4_all_success(self):
        expected_result = {
            "status": True,
            "route_error": [],
            "device_route_error": []
        }
        self._test_check_router_routes(expected_result=expected_result)

    def test_check_router_routes_ipv4_miss_define_route(self):
        expected_result = {
            "status": False,
            "route_error": [{"destination": "***********/24",
                             "nexthop": "************"}],
            "device_route_error": []
        }
        self._test_check_router_routes(error_route='define',
                                       expected_result=expected_result)

    def test_check_router_routes_ipv4_miss_default_route(self):
        expected_result = {
            "status": False,
            "route_error":
                ["default via ************** dev qg-3fd3e92d-3e"],
            "device_route_error": []
        }
        self._test_check_router_routes(error_route='default',
                                       expected_result=expected_result)

    def test_check_router_routes_ipv4_miss_qr_route(self):
        expected_result = {
            "status": False,
            "route_error": [],
            "device_route_error": [
                {"device": "qr-dc8e7e3b-9c",
                 "route": "***********/24 dev qr-dc8e7e3b-9c proto "
                          "kernel scope link src ***********"}]
        }
        self._test_check_router_routes(error_route='qr-',
                                       expected_result=expected_result)

    def test_check_router_routes_ipv4_miss_qg_route(self):
        expected_result = {
            "status": False,
            "route_error": [],
            "device_route_error": [
                {"device": "qg-3fd3e92d-3e",
                 "route": "************/24 dev qg-3fd3e92d-3e proto "
                          "kernel scope link src **************"}]
        }
        self._test_check_router_routes(error_route='qg-',
                                       expected_result=expected_result)

    def test_check_router_routes_ipv4_miss_ha_route(self):
        expected_result = {
            "status": False,
            "route_error": [],
            "device_route_error": [
                {"device": "ha-bfb187e5-8d",
                 "route": "*************/18 dev ha-bfb187e5-8d proto "
                          "kernel scope link src **************"},
                {"device": "ha-bfb187e5-8d",
                 "route": "***********/24 dev ha-bfb187e5-8d proto "
                           "kernel scope link src ************"}]
        }
        self._test_check_router_routes(error_route='ha-',
                                       expected_result=expected_result)

    def test_check_external_gateway_iptables_ipv4_rules_success(self):
        ri = self._create_ha_router()
        ri.ex_gw_port = {
            'fixed_ips': [{'ip_address': '**************'}]
        }
        ns_tables = {'ipv4': {
            'nat': [
                '-A neutron-l3-agent-snat -o qg-3fd3e92d-3e -j SNAT '
                '--to-source **************',
                '-A neutron-l3-agent-snat -m mark ! --mark 0x2/0xffff '
                '-m conntrack --ctstate DNAT -j SNAT --to-source '
                '**************',
                '-A neutron-l3-agent-POSTROUTING ! -i qg-3fd3e92d-3e'
                ' ! -o qg-3fd3e92d-3e -m conntrack ! --ctstate DNAT'
                ' -j ACCEPT'],
            'mangle': [
                '-A neutron-l3-agent-mark -i qg-3fd3e92d-3e -j MARK'
                ' --set-xmark 0x2/0xffff'],
            'filter': [],
            'raw': []
        }}
        result = {"status": True,
                  "iptables_error": []}
        interface_name = 'qg-3fd3e92d-3e'
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri._snat_enabled = True
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.agent_conf.external_ingress_mark = '0x2'
        ri.check_external_gateway_iptables_ipv4_rules(
            interface_name, result, ns_tables)
        self.assertTrue(result['status'])
        self.assertEqual([], result['iptables_error'])

    def test_check_external_gateway_iptables_ipv4_rules_nat_err(self):
        ri = self._create_ha_router()
        ri.ex_gw_port = {
            'fixed_ips': [{'ip_address': '**************'}]
        }
        ns_tables = {'ipv4': {
            'nat': [],
            'mangle': [
                '-A neutron-l3-agent-mark -i qg-3fd3e92d-3e -j MARK'
                ' --set-xmark 0x2/0xffff'],
            'filter': [],
            'raw': []
        }}
        result = {"status": True,
                  "iptables_error": []}
        interface_name = 'qg-3fd3e92d-3e'
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri._snat_enabled = True
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.agent_conf.external_ingress_mark = '0x2'
        ri.check_external_gateway_iptables_ipv4_rules(
            interface_name, result, ns_tables)
        err_rules = [('snat',
                      '-A neutron-l3-agent-snat -o qg-3fd3e92d-3e -j SNAT '
                      '--to-source **************'),
                     ('POSTROUTING',
                      '-A neutron-l3-agent-POSTROUTING ! -i qg-3fd3e92d-3e'
                      ' ! -o qg-3fd3e92d-3e -m conntrack ! --ctstate DNAT'
                      ' -j ACCEPT'),
                     ('snat',
                      '-A neutron-l3-agent-snat -m mark ! --mark 0x2/0xffff '
                      '-m conntrack --ctstate DNAT -j SNAT --to-source '
                      '**************')]
        error_res = []
        for chain, rule in err_rules:
            error_res.append("Missing fip iptables rule on "
                             "%s chain in nat table: %s." % (chain, rule))
        self.assertFalse(result['status'])
        self.assertEqual(result['iptables_error'], error_res)

    def test_check_external_gateway_iptables_ipv4_rules_mangle_err(self):
        ri = self._create_ha_router()
        ri.ex_gw_port = {
            'fixed_ips': [{'ip_address': '**************'}]
        }
        ns_tables = {'ipv4': {
            'nat': [
                '-A neutron-l3-agent-snat -o qg-3fd3e92d-3e -j SNAT '
                '--to-source **************',
                '-A neutron-l3-agent-snat -m mark ! --mark 0x2/0xffff '
                '-m conntrack --ctstate DNAT -j SNAT --to-source '
                '**************',
                '-A neutron-l3-agent-POSTROUTING ! -i qg-3fd3e92d-3e'
                ' ! -o qg-3fd3e92d-3e -m conntrack ! --ctstate DNAT'
                ' -j ACCEPT'],
            'mangle': [],
            'filter': [],
            'raw': []
        }}
        result = {"status": True,
                  "iptables_error": []}
        interface_name = 'qg-3fd3e92d-3e'
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri._snat_enabled = True
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.agent_conf.external_ingress_mark = '0x2'
        ri.check_external_gateway_iptables_ipv4_rules(
            interface_name, result, ns_tables)
        err_rules = [('mark',
                      '-A neutron-l3-agent-mark -i qg-3fd3e92d-3e -j MARK'
                      ' --set-xmark 0x2/0xffff')]
        error_res = []
        for chain, rule in err_rules:
            error_res.append("Missing fip iptables rule on "
                             "%s chain in mangle table: %s." % (chain, rule))
        self.assertFalse(result['status'])
        self.assertEqual(result['iptables_error'], error_res)

    def test_check_external_gateway_iptables_ipv6_rules_success(self):
        ri = self._create_ha_router()
        ri.ex_gw_port = {
            'fixed_ips': [{'ip_address': '2202:db8:cafe:1::17a'}]
        }
        ns_tables = {'ipv6': {
            'nat': [
                '-A neutron-l3-agent-snat -o qg-3fd3e92d-3e -j SNAT '
                '--to-source 2202:db8:cafe:1::17a',
                '-A neutron-l3-agent-snat -m mark ! --mark 0x2/0xffff '
                '-m conntrack --ctstate DNAT -j SNAT --to-source '
                '2202:db8:cafe:1::17a',
                '-A neutron-l3-agent-POSTROUTING ! -i qg-3fd3e92d-3e'
                ' ! -o qg-3fd3e92d-3e -m conntrack ! --ctstate DNAT'
                ' -j ACCEPT'],
            'mangle': [
                '-A neutron-l3-agent-mark -i qg-3fd3e92d-3e -j MARK'
                ' --set-xmark 0x2/0xffff'],
            'filter': [],
            'raw': []
        }}
        result = {"status": True,
                  "iptables_error": []}
        interface_name = 'qg-3fd3e92d-3e'
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri._snat66_enabled = True
        ri.agent_conf.enable_nat6to6 = True
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.agent_conf.external_ingress_mark = '0x2'
        ri.check_external_gateway_iptables_ipv6_rules(
            interface_name, result, ns_tables)
        self.assertTrue(result['status'])
        self.assertEqual([], result['iptables_error'])

    def test_check_floating_ip_iptables_success(self):
        ri = self._create_ha_router()
        ns_tables = {'ipv4': {
            'nat': [
                '-A neutron-l3-agent-OUTPUT -d **************/32 '
                '-j DNAT --to-destination **************',
                '-A neutron-l3-agent-PREROUTING -d **************/32 '
                '-j DNAT --to-destination **************',
                '-A neutron-l3-agent-float-snat -s **************/32 '
                '-j SNAT --to-source **************'],
            'mangle': [],
            'filter': [],
            'raw': []
        }}
        fips = [{
            'fixed_ip_address': '**************',
            'floating_ip_address': '**************',
            'fip_type': 'floatingip'}]
        ri.get_floating_ips = mock.Mock(return_value=fips)
        result = {"status": True,
                  "iptables_error": []}
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.check_floating_ip_iptables(result, ns_tables)
        self.assertTrue(result['status'])
        self.assertEqual([], result['iptables_error'])

    def test_check_floating_ip_iptables_err(self):
        ri = self._create_ha_router()
        ns_tables = {'ipv4': {
            'nat': [],
            'mangle': [],
            'filter': [],
            'raw': []
        }}
        fips = [{
            'fixed_ip_address': '**************',
            'floating_ip_address': '**************',
            'fip_type': 'floatingip'}]
        ri.get_floating_ips = mock.Mock(return_value=fips)
        result = {"status": True,
                  "iptables_error": []}
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.check_floating_ip_iptables(result, ns_tables)
        err_rules = [('PREROUTING',
                      '-A neutron-l3-agent-PREROUTING -d **************/32 '
                      '-j DNAT --to-destination **************'),
                     ('OUTPUT',
                      '-A neutron-l3-agent-OUTPUT -d **************/32 '
                      '-j DNAT --to-destination **************'),
                     ('float-snat',
                      '-A neutron-l3-agent-float-snat -s **************/32 '
                      '-j SNAT --to-source **************')]
        error_res = []
        for chain, rule in err_rules:
            error_res.append("Missing fip iptables rule on "
                             "%s chain in nat table: %s." % (chain, rule))
        self.assertFalse(result['status'])
        self.assertEqual(result['iptables_error'], error_res)

    def test_check_floating_ip_iptables_ipv6_success(self):
        ri = self._create_ha_router()
        ns_tables = {'ipv6': {
            'nat': [
                '-A neutron-l3-agent-OUTPUT -d 2202:db8:cafe:1::17a/128 '
                '-j DNAT --to-destination ipv6_port',
                '-A neutron-l3-agent-PREROUTING -d 2202:db8:cafe:1::17a/128 '
                '-j DNAT --to-destination ipv6_port',
                '-A neutron-l3-agent-float-snat -s ipv6_port/128 '
                '-j SNAT --to-source 2202:db8:cafe:1::17a'],
            'mangle': [],
            'filter': [],
            'raw': []
        }}
        fips = [{
            'fixed_ip_address': 'ipv6_port',
            'floating_ip_address': '2202:db8:cafe:1::17a',
            'fip_type': 'floatingip'}]
        ri.get_floating_ips = mock.Mock(return_value=fips)
        ri.agent_conf.enable_nat6to6 = True
        result = {"status": True,
                  "iptables_error": []}
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.check_floating_ip_iptables(result, ns_tables)
        self.assertTrue(result['status'])
        self.assertEqual([], result['iptables_error'])

    def test_check_floating_ip_iptables_ipv6_err(self):
        ri = self._create_ha_router()
        ns_tables = {'ipv4': {
            'nat': [],
            'mangle': [],
            'filter': [],
            'raw': []
        }}
        fips = [{
            'fixed_ip_address': 'ipv6_port',
            'floating_ip_address': '2202:db8:cafe:1::17a',
            'fip_type': 'floatingip'}]
        ri.get_floating_ips = mock.Mock(return_value=fips)
        ri.agent_conf.enable_nat6to6 = True
        result = {"status": True,
                  "iptables_error": []}
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.check_floating_ip_iptables(result, ns_tables)
        err_rules = [('PREROUTING',
                      '-A neutron-l3-agent-PREROUTING -d '
                      '2202:db8:cafe:1::17a/128 -j DNAT '
                      '--to-destination ipv6_port'),
                     ('OUTPUT',
                      '-A neutron-l3-agent-OUTPUT -d '
                      '2202:db8:cafe:1::17a/128 -j DNAT '
                      '--to-destination ipv6_port'),
                     ('float-snat',
                      '-A neutron-l3-agent-float-snat -s ipv6_port/128 '
                      '-j SNAT --to-source 2202:db8:cafe:1::17a')]
        error_res = []
        for chain, rule in err_rules:
            error_res.append("Missing fip iptables rule on "
                             "%s chain in nat table: %s." % (chain, rule))
        self.assertFalse(result['status'])
        self.assertEqual(result['iptables_error'], error_res)

    def test_check_esnat_rule_success(self):
        ri = self._create_ha_router()
        elastic_snat_rules = [{
            'internal_cidrs': ['***********/24'],
            'floating_ip_address': '**************'
        }]
        interface_name = 'qg-4365ce6b-d2'
        ns_tables = {'ipv4': {
            'nat': [
                '-A neutron-l3-agent-esnat -s ***********/24 '
                '-o qg-4365ce6b-d2 -j SNAT --to-source **************'],
            'mangle': [],
            'filter': [],
            'raw': []
        }}
        result = {"status": True,
                  "iptables_error": []}
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri._snat_enabled = True
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.check_esnat_rules(
            interface_name, elastic_snat_rules, result, ns_tables)
        self.assertTrue(result['status'])
        self.assertEqual([], result['iptables_error'])

    def test_check_esnat_rule_err(self):
        ri = self._create_ha_router()
        elastic_snat_rules = [{
            'internal_cidrs': ['***********/24'],
            'floating_ip_address': '**************'
        }]
        interface_name = 'qg-4365ce6b-d2'
        ns_tables = {'ipv4': {
            'nat': [],
            'mangle': [],
            'filter': [],
            'raw': []
        }}
        result = {"status": True,
                  "iptables_error": []}
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri._snat_enabled = True
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.check_esnat_rules(
            interface_name, elastic_snat_rules, result, ns_tables)
        err_rules = [('esnat',
                      '-A neutron-l3-agent-esnat -s ***********/24 '
                      '-j SNAT -o qg-4365ce6b-d2 --to-source **************')]
        error_res = []
        for chain, rule in err_rules:
            error_res.append("Missing fip iptables rule on "
                             "%s chain in nat table: %s." % (chain, rule))
        self.assertFalse(result['status'])
        self.assertEqual(result['iptables_error'], error_res)

    def test_check_port_forwarding_rules_success(self):
        ri = self._create_ha_router()
        ri.router['_pf_floatingips'] = [{
            'floating_ip_address': '**************',
            'port_forwardings': [{
                'external_port_range': '34:34',
                'protocol': 'tcp',
                'internal_ip_address': '************',
                'internal_port_range': '1234:1234',
                'id': '33e6978a-8f28-4479-933f-ca931a8c37d2'}]
        }]
        ns_tables = {'ipv4': {
            'nat': [
                '-A neutron-l3-agent-pf-33e6978a -d **************/32 '
                '-p tcp -m tcp --dport 34 -j DNAT --to-destination '
                '************:1234'],
            'mangle': [],
            'filter': [],
            'raw': []
        }}
        result = {"status": True,
                  "iptables_error": []}
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri._snat_enabled = True
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.check_port_forwarding_rules(result, ns_tables)
        self.assertTrue(result['status'])
        self.assertEqual([], result['iptables_error'])

    def test_check_port_forwarding_rules_err(self):
        ri = self._create_ha_router()
        ri.router['_pf_floatingips'] = [{
            'floating_ip_address': '**************',
            'port_forwardings': [{
                'external_port_range': '34:34',
                'protocol': 'tcp',
                'internal_ip_address': '************',
                'internal_port_range': '1234:1234',
                'id': '33e6978a-8f28-4479-933f-ca931a8c37d2'}]
        }]
        ns_tables = {'ipv4': {
            'nat': [],
            'mangle': [],
            'filter': [],
            'raw': []
        }}
        result = {"status": True,
                  "iptables_error": []}
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri._snat_enabled = True
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.check_port_forwarding_rules(result, ns_tables)
        err_rules = [('pf-33e6978a',
                      '-A neutron-l3-agent-pf-33e6978a -d **************/32 '
                      '-p tcp -m tcp --dport 34 -j DNAT --to-destination '
                      '************:1234')]
        error_res = []
        for chain, rule in err_rules:
            error_res.append("Missing fip iptables rule on "
                             "%s chain in nat table: %s." % (chain, rule))
        self.assertFalse(result['status'])
        self.assertEqual(result['iptables_error'], error_res)

    def test_check_address_scope_iptables_success(self):
        ri = self._create_ha_router()
        address_scope_mark = {
            4: {'qg-4365ce6b-d2': '0x4000000/0xffff0000',
                'qr-a47e1d53-76': '0x4000000/0xffff0000',
                'qr-********-2b': '0x4000000/0xffff0000'},
            6: {'qr-1c364cfc-15': '0x4000000/0xffff0000'}}
        ri._get_address_scope_mark = mock.Mock(return_value=address_scope_mark)
        ri.get_ex_gw_port = mock.Mock(
            return_value={'id': '4365ce6b-d259-4844-8398-b890da2cccd5'})
        ri.check_floating_ip_address_scope_rules = mock.Mock()
        ri.pd_subnets = {}
        ns_tables = {'ipv4': {
            'nat': [],
            'mangle': [
                '-A neutron-l3-agent-PREROUTING -m connmark ! --mark'
                ' 0x0/0xffff0000 -j CONNMARK --restore-mark --nfmask '
                '0xffff0000 --ctmask 0xffff0000',
                '-A neutron-l3-agent-float-snat -m connmark --mark '
                '0x0/0xffff0000 -j CONNMARK --save-mark --nfmask '
                '0xffff0000 --ctmask 0xffff0000',
                '-A neutron-l3-agent-scope -i qg-4365ce6b-d2 -j MARK '
                '--set-xmark 0x4000000/0xffff0000',
                '-A neutron-l3-agent-scope -i qr-a47e1d53-76 -j MARK '
                '--set-xmark 0x4000000/0xffff0000',
                '-A neutron-l3-agent-scope -i qr-********-2b -j MARK '
                '--set-xmark 0x4000000/0xffff0000'],
            'filter': [
                '-A neutron-l3-agent-scope -o qg-4365ce6b-d2 '
                '-m mark ! --mark 0x4000000/0xffff0000 -j DROP',
                '-A neutron-l3-agent-scope -o qr-a47e1d53-76 -m '
                'mark ! --mark 0x4000000/0xffff0000 -j DROP',
                '-A neutron-l3-agent-scope -o qr-********-2b -m '
                'mark ! --mark 0x4000000/0xffff0000 -j DROP'],
            'raw': []
        }, 'ipv6': {
            'nat': [],
            'mangle': [
                '-A neutron-l3-agent-PREROUTING -m connmark ! '
                '--mark 0x0/0xffff0000 -j CONNMARK --restore-mark'
                ' --nfmask 0xffff0000 --ctmask 0xffff0000',
                '-A neutron-l3-agent-scope -i qr-1c364cfc-15 -j MARK '
                '--set-xmark 0x4000000/0xffff0000'],
            'filter': [
                '-A neutron-l3-agent-scope -o qr-1c364cfc-15 '
                '-m mark ! --mark 0x4000000/0xffff0000 -j DROP'],
            'raw': []
        }}
        result = {"status": True,
                  "iptables_error": []}
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri._snat_enabled = True
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.check_port_forwarding_rules(result, ns_tables)
        self.assertTrue(result['status'])
        self.assertEqual([], result['iptables_error'])

    def test_check_fip_denied_ports_rules_ipv4(self):
        ri = self._create_ha_router()
        ns_tables = {'ipv4': {
            'nat': [],
            'mangle': [],
            'filter': [],
            'raw': [
                '-A neutron-l3-agent-denied-port -d **************/32'
                ' -i qg-69a1b570-a2 -p tcp -m tcp --dport 8080 -j DROP',
                '-A neutron-l3-agent-denied-port -d **************/32 '
                '-i qg-69a1b570-a2 -p udp -m udp --dport 8080 -j DROP']
        }}
        ri.get_ex_gw_port = mock.Mock(return_value={
            'id': '69a1b570-a242-4b3c-9280-c3404f1ab51d'})
        fips = [{
            'fixed_ip_address': '**************',
            'floating_ip_address': '**************',
            'fip_type': 'floatingip',
            'denied_port_numbers': ['8080']}]
        ri.get_floating_ips = mock.Mock(return_value=fips)
        ri.get_port_forwarding_fips = mock.Mock(return_value=[])
        result = {"status": True,
                  "iptables_error": []}
        ns_tables = ri._normalize_ns_tables(ns_tables)
        ri.iptables_manager.wrap_name = 'neutron-l3-agent'
        ri.check_fip_denied_ports_rules(result, ns_tables)
        self.assertTrue(result['status'])
        self.assertEqual([], result['iptables_error'])
