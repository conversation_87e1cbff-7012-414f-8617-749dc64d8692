# Copyright 2024 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""Add traffic mirror

Revision ID: 661199622a97
Revises: efa57beb2abe
Create Date: 2024-03-13 10:15:53.532727

"""

from alembic import op
import sqlalchemy as sa

from neutron_lib.db import constants

# revision identifiers, used by Alembic.
revision = '661199622a97'
down_revision = 'efa57beb2abe'


rule_direction_enum = sa.Enum('ingress', 'egress',
                              name='traffic_mirror_filter_rules_direction')
rule_action_enum = sa.Enum('accept', 'reject',
                           name='traffic_mirror_filter_rules_action')


def upgrade():
    exist_traffic_mirror_sessions = False
    exist_traffic_mirror_source_bindings = False
    exist_traffic_mirror_filters = False
    exist_traffic_mirror_filter_rules = False
    exist_traffic_mirror_session_segments = False
    exist_mirror_tunnel_network = False

    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for table in insp.get_table_names():
        if table == 'traffic_mirror_sessions':
            exist_traffic_mirror_sessions = True
        if table == 'traffic_mirror_source_bindings':
            exist_traffic_mirror_source_bindings = True
        if table == 'traffic_mirror_filters':
            exist_traffic_mirror_filters = True
        if table == 'traffic_mirror_filter_rules':
            exist_traffic_mirror_filter_rules = True
        if table == 'traffic_mirror_session_segments':
            exist_traffic_mirror_session_segments = True
        if table == 'mirror_tunnel_network':
            exist_mirror_tunnel_network = True

    if not exist_traffic_mirror_filters:
        op.create_table(
            'traffic_mirror_filters',
            sa.Column('id', sa.String(length=constants.UUID_FIELD_SIZE),
                      primary_key=True,
                      nullable=False),
            sa.Column('name', sa.String(length=255), nullable=True),
            sa.Column('project_id',
                      sa.String(length=constants.PROJECT_ID_FIELD_SIZE),
                      nullable=True,
                      index=True),
            sa.Column('standard_attr_id', sa.BigInteger(), nullable=False),
            sa.ForeignKeyConstraint(['standard_attr_id'],
                                    ['standardattributes.id'],
                                    ondelete='CASCADE'),
            sa.UniqueConstraint('standard_attr_id')
        )

    if not exist_traffic_mirror_filter_rules:
        op.create_table(
            'traffic_mirror_filter_rules',
            sa.Column('id', sa.String(length=constants.UUID_FIELD_SIZE),
                      primary_key=True,
                      nullable=False),
            sa.Column('project_id',
                      sa.String(length=constants.PROJECT_ID_FIELD_SIZE),
                      nullable=True,
                      index=True),
            sa.Column('traffic_mirror_filter_id',
                      sa.String(length=constants.UUID_FIELD_SIZE),
                      nullable=False),
            sa.Column('direction', rule_direction_enum, nullable=False),
            sa.Column('ethertype', sa.String(length=40), nullable=False),
            sa.Column('protocol', sa.String(length=40), nullable=True),
            sa.Column('src_cidr', sa.String(length=255), nullable=True),
            sa.Column('dst_cidr', sa.String(length=255), nullable=True),
            sa.Column('src_port_range_min', sa.Integer(), nullable=True),
            sa.Column('src_port_range_max', sa.Integer(), nullable=True),
            sa.Column('dst_port_range_min', sa.Integer(), nullable=True),
            sa.Column('dst_port_range_max', sa.Integer(), nullable=True),
            sa.Column('action', rule_action_enum, nullable=False),
            sa.Column('priority', sa.Integer(), nullable=False),
            sa.Column('standard_attr_id', sa.BigInteger(), nullable=False),
            sa.ForeignKeyConstraint(['traffic_mirror_filter_id'],
                                    ['traffic_mirror_filters.id'],
                                    ondelete='CASCADE'),
            sa.ForeignKeyConstraint(['standard_attr_id'],
                                    ['standardattributes.id'],
                                    ondelete='CASCADE'),
            sa.UniqueConstraint('standard_attr_id')
        )

    if not exist_traffic_mirror_sessions:
        op.create_table(
            'traffic_mirror_sessions',
            sa.Column('id', sa.String(length=constants.UUID_FIELD_SIZE),
                      primary_key=True,
                      nullable=False),
            sa.Column('name', sa.String(length=255), nullable=True),
            sa.Column('project_id',
                      sa.String(length=constants.PROJECT_ID_FIELD_SIZE),
                      nullable=True,
                      index=True),
            sa.Column('traffic_mirror_filter_id',
                      sa.String(length=constants.UUID_FIELD_SIZE),
                      nullable=False),
            sa.Column('traffic_mirror_target_port_id',
                      sa.String(length=constants.UUID_FIELD_SIZE),
                      nullable=False),
            sa.Column('traffic_mirror_target_type',
                      sa.String(length=40), nullable=False),
            sa.Column('virtual_network_id', sa.Integer(), nullable=True),
            sa.Column('packet_length', sa.Integer(), nullable=True),
            sa.Column('priority', sa.Integer(), nullable=False),
            sa.Column('enabled', sa.Boolean(), nullable=True),
            sa.Column('standard_attr_id', sa.BigInteger(), nullable=False),
            sa.ForeignKeyConstraint(['standard_attr_id'],
                                    ['standardattributes.id'],
                                    ondelete='CASCADE'),
            sa.ForeignKeyConstraint(['traffic_mirror_filter_id'],
                                    ['traffic_mirror_filters.id']),
            sa.UniqueConstraint('standard_attr_id')
        )

    if not exist_traffic_mirror_source_bindings:
        op.create_table(
            'traffic_mirror_source_bindings',
            sa.Column('traffic_mirror_session_id',
                      sa.String(length=constants.UUID_FIELD_SIZE),
                      primary_key=True,
                      nullable=False),
            sa.Column('source_port_id',
                      sa.String(length=constants.UUID_FIELD_SIZE),
                      primary_key=True,
                      nullable=False),
            sa.ForeignKeyConstraint(
                ['traffic_mirror_session_id'],
                ['traffic_mirror_sessions.id'],
                ondelete='CASCADE')
        )

    if not exist_traffic_mirror_session_segments:
        op.create_table(
            'traffic_mirror_session_segments',
            sa.Column('traffic_mirror_session_id',
                      sa.String(length=36), nullable=False),
            sa.Column('segmentation_id', sa.Integer(), nullable=False),
            sa.ForeignKeyConstraint(['traffic_mirror_session_id'],
                                    ['traffic_mirror_sessions.id'],
                                    ondelete='CASCADE')
        )

    if not exist_mirror_tunnel_network:
        op.create_table(
            'mirror_tunnel_network',
            sa.Column('availability_zone', sa.String(length=255),
                      nullable=True),
            sa.Column('network_id',
                      sa.String(length=constants.UUID_FIELD_SIZE),
                      nullable=True)
        )
