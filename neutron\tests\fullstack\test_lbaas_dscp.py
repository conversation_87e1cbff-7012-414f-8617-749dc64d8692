#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
import re

from neutron_lib import constants
from oslo_utils import uuidutils

from neutron.common import utils as common_utils
from neutron.plugins.ml2.drivers.openvswitch.agent.common \
    import constants as p_const
from neutron.tests.common import net_helpers
from neutron.tests.fullstack import base
from neutron.tests.fullstack.resources import environment
from neutron.tests.fullstack.resources import machine
from neutron.tests.unit import testlib_api

load_tests = testlib_api.module_load_tests


class TestLbaasDscp(base.BaseFullStackTestCase):
    number_of_hosts = 1
    firewall_driver = 'openvswitch'

    def setUp(self):
        host_descriptions = [
            environment.HostDescription(
                l2_agent_type=constants.AGENT_TYPE_OVS,
                firewall_driver=self.firewall_driver,
                of_interface='native') for _ in range(self.number_of_hosts)]
        env = environment.Environment(
            environment.EnvironmentDescription(
                mech_drivers=self.firewall_driver,
                enable_lbaas_dscp=True),
            host_descriptions)
        super(TestLbaasDscp, self).setUp(env)

        self.tenant_id = uuidutils.generate_uuid()

        self.network = self.safe_client.create_network(
            self.tenant_id, name='net1',
            network_type=constants.TYPE_VLAN)
        self.subnet = self.safe_client.create_subnet(
            self.tenant_id, self.network['id'],
            cidr='10.0.0.0/24', name='subnet1')

    def _prepare_vms(self, device_owner="compute:test_ovs_dhcp"):
        port1 = self.safe_client.create_port(
            self.tenant_id,
            network_id=self.network['id'],
            hostname=self.environment.hosts[0].hostname,
            device_owner=device_owner,
            security_groups=[],
            port_security_enabled=False)

        port2 = self.safe_client.create_port(
            self.tenant_id,
            network_id=self.network['id'],
            hostname=self.environment.hosts[0].hostname,
            device_owner=device_owner,
            security_groups=[],
            port_security_enabled=False)
        sg = self.safe_client.create_security_group(self.tenant_id)
        self.safe_client.create_security_group_rule(
            self.tenant_id, sg['id'],
            remote_group_id=sg['id'], direction='ingress',
            ethertype=constants.IPv4,
            protocol=constants.PROTO_NAME_TCP,
            port_range_min=3333, port_range_max=3333)

        vm1 = self.useFixture(
            machine.FakeFullstackMachine(
                self.environment.hosts[0],
                self.network['id'],
                self.tenant_id,
                self.safe_client,
                neutron_port=port1))

        vm2 = self.useFixture(
            machine.FakeFullstackMachine(
                self.environment.hosts[0],
                self.network['id'],
                self.tenant_id,
                self.safe_client,
                neutron_port=port2))

        return machine.FakeFullstackMachinesList([vm1, vm2])

    def wait_for_update_dscp_learn_flows(self, vm, vif_port):
        def check_flows():
            dscp_flows = vm.bridge.dump_flows_for(table=p_const.DSCP_TABLE)
            pattern = (r'(cookie=|duration=|n_packets=|n_bytes=|idle_age=)'
                       r'[^,]*,\s*')
            cleaned_flow = re.sub(pattern, '', dscp_flows)
            cleaned_flow = cleaned_flow.strip()
            expected_flow = 'table=87, priority=20,tcp,reg5=0x{ofport},' \
                            'dl_dst={mac},nw_tos=44 actions=learn(table=70,' \
                            'idle_timeout=900,hard_timeout=1800,' \
                            'priority=100,eth_type=0x800,nw_proto=6,' \
                            'reg5=0x{ofport},' \
                            'NXM_OF_IP_SRC[]=NXM_OF_IP_DST[],' \
                            'NXM_OF_IP_DST[]=NXM_OF_IP_SRC[],' \
                            'NXM_OF_TCP_SRC[]=NXM_OF_TCP_DST[],' \
                            'NXM_OF_TCP_DST[]=NXM_OF_TCP_SRC[],' \
                            'load:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[])'.format(
                                mac=vif_port.vif_mac, ofport=vif_port.ofport)
            return expected_flow == cleaned_flow
        common_utils.wait_until_true(check_flows)

    def wait_for_extend_dscp_learn_flows(self, vm, vif_port):
        def check_flows():
            dscp_flows = vm.bridge.dump_flows_for(table=p_const.DSCP_TABLE)
            pattern = (r'(cookie=|duration=|n_packets=|n_bytes=|idle_age=)'
                       r'[^,]*,\s*')
            cleaned_flow = re.sub(pattern, '', dscp_flows)
            cleaned_flow = cleaned_flow.strip()
            expected_flows = [
                'table=87, priority=20,tcp,reg5=0x{ofport},'
                'dl_dst={mac},nw_tos=44 actions=learn(table=70,'
                'idle_timeout=900,hard_timeout=1800,'
                'priority=100,eth_type=0x800,nw_proto=6,'
                'reg5=0x{ofport},'
                'NXM_OF_IP_SRC[]=NXM_OF_IP_DST[],'
                'NXM_OF_IP_DST[]=NXM_OF_IP_SRC[],'
                'NXM_OF_TCP_SRC[]=NXM_OF_TCP_DST[],'
                'NXM_OF_TCP_DST[]=NXM_OF_TCP_SRC[],'
                'load:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[])'.format(
                    mac=vif_port.vif_mac, ofport=vif_port.ofport),
                'table=87, priority=20,udp,reg5=0x{ofport},'
                'dl_dst={mac},nw_tos=44 actions=learn(table=70,'
                'idle_timeout=900,hard_timeout=1800,'
                'priority=100,eth_type=0x800,nw_proto=17,'
                'reg5=0x{ofport},'
                'NXM_OF_IP_SRC[]=NXM_OF_IP_DST[],'
                'NXM_OF_IP_DST[]=NXM_OF_IP_SRC[],'
                'NXM_OF_UDP_SRC[]=NXM_OF_UDP_DST[],'
                'NXM_OF_UDP_DST[]=NXM_OF_UDP_SRC[],'
                'load:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[])'.format(
                    mac=vif_port.vif_mac, ofport=vif_port.ofport)]

            for flow in expected_flows:
                if flow not in cleaned_flow:
                    return False
            return True
        common_utils.wait_until_true(check_flows)

    def wait_for_no_dscp_learn_flows(self, vm, vif_port):
        def check_flows():
            flows = vm.bridge.dump_flows_for(
                table=p_const.DSCP_TABLE,
                dl_dst=vif_port.vif_mac
            )
            return not flows
        common_utils.wait_until_true(check_flows)

    def test_port_extend_dscp_learn_attrs_ovs_fw(self):
        """Test scenario

        1. Create two VMs using the vlan network and allow ingress tcp sg rule
        2. VM2 port add cloud attributes for dscp_learn specified tos and proto
        3. Check VM2 host add ovs flows in table 87
        4. VM2 port extend cloud attributes for dscp_learn
        5. Check table 87 add relevant flows
        """
        vms = self._prepare_vms()
        vms.block_until_all_boot()
        self.assert_connection(
            vms[1].namespace, vms[0].namespace, vms[0].ip, 3333,
            net_helpers.NetcatTester.TCP)

        dscp_attrs = {'port': {'cloud_attributes': {'dscp_learn': [{
            'tos': '44', 'protocol': 'tcp'}]}}}
        self.safe_client.create_port_cloud_attributes(
            vms[0].neutron_port['id'], dscp_attrs)

        vif_port = vms[0].bridge.get_vif_port_by_id(vms[0].neutron_port['id'])
        self.wait_for_update_dscp_learn_flows(vms[0], vif_port)
        dscp_attrs = {'cloud_attributes': {'dscp_learn': [{
            'tos': '44', 'protocol': 'udp'}]}}
        self.safe_client.extend_port_cloud_attributes(
            vms[0].neutron_port['id'], dscp_attrs)
        self.wait_for_extend_dscp_learn_flows(vms[0], vif_port)

    def test_port_add_and_delete_all_attrs_belong_to_dscp_learn_ovs_fw(self):
        """Test scenario

        1. Create two VMs using the vlan network and allow ingress tcp sg rule
        2. VM2 port add cloud attributes for dscp_learn specified tos and prot
        3. Check VM2 host add ovs flows in table 87
        4. Delete VM2 port cloud attrs with assigned key
        5. Check table 87 delete relevant flows
        """
        vms = self._prepare_vms()
        vms.block_until_all_boot()
        self.assert_connection(
            vms[1].namespace, vms[0].namespace, vms[0].ip, 3333,
            net_helpers.NetcatTester.TCP)
        dscp_attrs = {'port': {'cloud_attributes': {'dscp_learn': [{
            'tos': '44', 'protocol': 'tcp'}]}}}
        self.safe_client.create_port_cloud_attributes(
            vms[0].neutron_port['id'], dscp_attrs)
        vif_port = vms[0].bridge.get_vif_port_by_id(vms[0].neutron_port['id'])
        self.wait_for_update_dscp_learn_flows(vms[0], vif_port)

        self.safe_client.client.delete_port_cloud_attribute(
            vms[0].neutron_port['id'], attr_key='dscp_learn')
        self.wait_for_no_dscp_learn_flows(vms[0], vif_port)

    def test_port_add_and_delete_one_attrs_belong_to_dscp_learn_ovs_fw(self):
        """Test scenario

        1. Create two VMs using the vlan network and allow ingress tcp sg rule
        2. VM2 port add cloud attributes for dscp_learn specified tos and prot
        3. Check VM2 host add ovs flows in table 87
        4. Delete VM2 port cloud attrs with assigned attrs: {key: value}
        5. Check table 87 delete relevant flows
        """
        vms = self._prepare_vms()
        vms.block_until_all_boot()
        dscp_attrs = {'port': {'cloud_attributes': {'dscp_learn': [{
            'tos': '44', 'protocol': 'tcp'}]}}}
        self.safe_client.create_port_cloud_attributes(
            vms[1].neutron_port['id'], dscp_attrs)
        vif_port = vms[1].bridge.get_vif_port_by_id(vms[1].neutron_port['id'])
        self.wait_for_update_dscp_learn_flows(vms[1], vif_port)
        self.safe_client.client.delete_port_cloud_attributes(
            vms[1].neutron_port['id'], body=dscp_attrs['port'])
        self.wait_for_no_dscp_learn_flows(vms[1], vif_port)

    def assert_connection(self, *args, **kwargs):
        netcat = net_helpers.NetcatTester(*args, **kwargs)

        def test_connectivity():
            try:
                return netcat.test_connectivity()
            except RuntimeError:
                return False

        try:
            common_utils.wait_until_true(test_connectivity)
        finally:
            netcat.stop_processes()
