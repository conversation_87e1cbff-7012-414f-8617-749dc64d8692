# Copyright 2020 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add meter vpcs table

Revision ID: 8bd5e6822956
Revises: f5eb548334bc
Create Date: 2020-06-06 13:38:33.576090

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '8bd5e6822956'
down_revision = 'f5eb548334bc'


def upgrade():
    exist_meteringvpcs = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for table in insp.get_table_names():
        if table == 'meteringvpcs':
            exist_meteringvpcs = True
            break

    if not exist_meteringvpcs:
        op.create_table(
            'meteringvpcs',
            sa.Column('id', sa.String(length=36), nullable=False),
            sa.Column('description', sa.String(length=255), nullable=True),
            sa.Column('router_id', sa.String(length=64), nullable=False),
            sa.Column('destination_ip', sa.String(length=64), nullable=False),
            sa.Column('project_id', sa.String(length=255), nullable=False),
            sa.PrimaryKeyConstraint('id')
            # extend_existing=True
        )
