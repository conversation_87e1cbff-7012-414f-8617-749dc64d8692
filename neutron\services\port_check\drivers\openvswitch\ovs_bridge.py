#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import re

from ryu.lib import ofctl_string
from ryu.lib import ofctl_v1_3
from ryu.lib.packet import ether_types
from ryu.lib.packet import in_proto
from ryu.ofproto import ofproto_parser
import six

from neutron.plugins.ml2.drivers.openvswitch.agent.openflow.native \
    import br_int
from neutron.plugins.ml2.drivers.openvswitch.agent.openflow.native \
    import br_phys
from neutron.services.port_check.drivers.openvswitch import ofctl_utils
from neutron.services.port_check import plugin


class OVSIntegrationBridge(br_int.OVSIntegrationBridge):
    def __init__(self, *args, **kwargs):
        self.of_map = kwargs.pop('of_map')
        self.reports = plugin.Reports()
        self.br = self
        super(OVSIntegrationBridge, self).__init__(*args, **kwargs)

    def __getattribute__(self, attr):
        exc = AttributeError("Method/attr %r is not allowed" % attr)
        # Just to make sure that we don't call ovs flows modification
        # methods:
        forbidden_attrs = ['cleanup_flows', 'run_ofctl']
        allowed_install = ['install_instructions', 'install_goto',
                           'install_drop', 'install_normal',
                           'install_apply_actions', 'install_output']
        if attr in forbidden_attrs:
            raise exc
        if attr.startswith('install') or attr.startswith('uninstall'):
            # 'install_instructions' is override main
            if attr not in allowed_install:
                raise exc
        return super(OVSIntegrationBridge, self).__getattribute__(attr)

    def install_instructions(self, instructions,
                             table_id=0, priority=0,
                             match=None, active_bundle=None, **match_kwargs):
        (dp, ofp, ofpp) = self._get_dp()
        match = self._match(ofp, ofpp, match, **match_kwargs)

        instructions_str = ''
        if isinstance(instructions, six.string_types):
            instructions_str = instructions
            jsonlist = ofctl_string.ofp_instruction_from_str(
                ofp, instructions)
            instructions = ofproto_parser.ofp_instruction_from_jsondict(
                dp, jsonlist)
        flow = ofpp.OFPFlowStats(table_id=table_id, priority=priority,
                                 match=match, instructions=instructions)
        if not self.of_map.exists(dp.id, flow):
            match_str = ofctl_v1_3.match_to_str(match)
            match_str = ','.join('%s=%s' % kv for kv in match_str.items())
            if not instructions_str:
                instructions_str = ofctl_utils.actions_to_str(
                    flow.instructions)
                instructions_str = ','.join(instructions_str)
            msg = ('No flow: %s table=%s, priority=%s,%s actions=%s',
                   self.br.br_name, table_id, priority,
                   match_str, instructions_str)
            self.reports.add(*msg)

    @staticmethod
    def _numeric_ct_state(ct_state):
        CT_BITS = dict([(b, a) for a, b in enumerate([
            "new", "est", "rel", "rpl",
            "inv", "trk", "snat", "dnat"])])
        SENTINEL = '+-'
        ct_state += SENTINEL
        val = 0
        mask = 0
        while ct_state != SENTINEL:
            pm = ct_state[0]
            ct_state = ct_state[1:]
            nextpos = min([ct_state.find(sep) for sep in ['+', '-']])
            bit = 1 << CT_BITS[ct_state[:nextpos]]
            mask |= bit
            if pm == '+':
                val |= bit
            ct_state = ct_state[nextpos:]
        return (val, mask)

    @staticmethod
    def _mod_act_to_set_field(actions):
        if 'mod_vlan_vid' in actions:
            vlan = re.search(r'mod_vlan_vid:(\d+)', actions).groups()[0]
            vlan = int(vlan) | 0x1000
            actions = re.sub(r'mod_vlan_vid:\d+',
                             r'set_field:{}->vlan_vid'.format(vlan), actions)
        return actions

    @staticmethod
    def _trans_proto(proto, **kwargs):
        remap = {
            'arp': {'eth_dst': 'arp_tha',
                    'eth_src': 'arp_sha',
                    'ipv4_dst': 'arp_tpa',
                    'ipv4_src': 'arp_spa'}
        }
        if proto == 'arp':
            for key, remap_key in remap['arp'].items():
                if key in kwargs:
                    kwargs[remap_key] = kwargs.pop(key)
            kwargs['eth_type'] = ether_types.ETH_TYPE_ARP
        # trans _write_proto in stateless firewall
        if ',' in proto:
            params = proto.split(',')
            eth_type, val = params[0].split('=')
            kwargs[eth_type] = int(val, 16)
            ip_proto, val = params[1].split('=')
            kwargs[ip_proto] = int(val)
        elif '=' in proto:
            eth_type, val = proto.split('=')
            kwargs[eth_type] = int(val, 16)
        return kwargs

    # Due to ovs-ofctl based 'add_flow' is called during ovs driver
    # initialization, make it to work via native os-ken API to simplify
    # port check plugin logic
    def add_flow(self, **kwargs):
        remap = {
            'dl_dst': 'eth_dst',
            'dl_src': 'eth_src',
            'dl_vlan': 'vlan_vid',
            'dl_type': 'eth_type',
            'nw_src': 'ipv4_src',
            'nw_dst': 'ipv4_dst',
            'nw_proto': 'ip_proto',
            'icmp_type': 'icmpv6_type',
            'tp_dst': 'udp_dst',
            'tp_src': 'udp_src',
            'nd_target': 'ipv6_nd_target',
        }
        convert = {
            'eth_type': ofctl_utils.str_to_int,
            'ct_mark': ofctl_utils.str_to_int,
            'dl_vlan': ofctl_utils.to_match_vid,
            'vlan_vid': ofctl_utils.to_match_vid,
            'vlan_tci': ofctl_utils.to_match_masked_int,
            'eth_dst': ofctl_utils.to_match_eth,
            'ipv6_src': ofctl_utils.to_match_ip,
            'udp_dst': ofctl_utils.to_match_masked_int,
            'tcp_dst': ofctl_utils.to_match_masked_int,
            'ct_state': self._numeric_ct_state,
        }
        for key, remap_key in remap.items():
            if key in kwargs:
                kwargs[remap_key] = kwargs.pop(key)
        for key, func in convert.items():
            if key in kwargs:
                kwargs[key] = func(kwargs[key])
        if 'actions' in kwargs:
            kwargs['actions'] = kwargs['actions'].replace(
                'strip_vlan', 'pop_vlan')
            kwargs['actions'] = self._mod_act_to_set_field(kwargs['actions'])
        if 'proto' in kwargs:
            proto = kwargs.pop('proto')
            kwargs = self._trans_proto(proto, **kwargs)
        if 'ip_proto' in kwargs and 'icmpv6_type' in kwargs:
            if kwargs['ip_proto'] == in_proto.IPPROTO_ICMP:
                kwargs['icmpv4_type'] = kwargs.pop('icmpv6_type')
        if 'ipv6_src' in kwargs and kwargs['ipv6_src'] == ('::', '::'):
            kwargs.pop('ipv6_src')

        actions = kwargs.pop('actions')
        # OfctlActionConverter doesn't support load,learn action
        actions = re.sub(r'load:.*?,', '', actions)
        actions = re.sub(r'learn\(.*?\),?', '', actions)
        table_id = kwargs.pop('table')
        self.install_instructions(actions, table_id=table_id, **kwargs)

    # 'delete_flows' is called during stateless firewall, need overwrite
    def delete_flows(self, **kwargs):
        pass


class OVSPhysicalBridge(br_phys.OVSPhysicalBridge):
    def __init__(self, *args, **kwargs):
        self.of_map = kwargs.pop('of_map')
        self.reports = plugin.Reports()
        self.br = self
        super(OVSPhysicalBridge, self).__init__(*args, **kwargs)

    def __getattribute__(self, attr):
        exc = AttributeError("Method/attr %r is not allowed" % attr)
        # Just to make sure that we don't call ovs flows modification
        # methods:
        forbidden_attrs = ['cleanup_flows', 'run_ofctl']
        allowed_install = ['install_instructions', 'install_goto',
                           'install_drop', 'install_apply_actions',
                           'install_normal', 'install_output']
        if attr in forbidden_attrs:
            raise exc
        if attr.startswith('install') or attr.startswith('uninstall'):
            # 'install_instructions' is override main
            if attr not in allowed_install:
                raise exc
        return super(OVSPhysicalBridge, self).__getattribute__(attr)

    def install_instructions(self, instructions,
                             table_id=0, priority=0,
                             match=None, active_bundle=None, **match_kwargs):
        (dp, ofp, ofpp) = self._get_dp()
        match = self._match(ofp, ofpp, match, **match_kwargs)
        if isinstance(instructions, six.string_types):
            jsonlist = ofctl_string.ofp_instruction_from_str(
                ofp, instructions)
            instructions = ofproto_parser.ofp_instruction_from_jsondict(
                dp, jsonlist)
        flow = ofpp.OFPFlowStats(table_id=table_id, priority=priority,
                                 match=match, instructions=instructions)
        if not self.of_map.exists(dp.id, flow):
            match_str = ofctl_v1_3.match_to_str(match)
            match_str = ','.join('%s=%s' % kv for kv in match_str.items())
            instructions = ofctl_utils.actions_to_str(flow.instructions)
            instructions = ','.join(instructions)
            msg = ('No flow: %s table=%s, priority=%s,%s actions=%s',
                   self.br.br_name, table_id, priority,
                   match_str, instructions)
            self.reports.add(*msg)
