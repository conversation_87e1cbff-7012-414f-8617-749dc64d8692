#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.


from neutron_lib.api import converters
from neutron_lib.api.definitions import network
from neutron_lib.api.definitions import port
from neutron_lib.db import constants as db_const

from neutron.services.flow_log.common import constants as log_const

# The name of the extension.
NAME = 'Flow Log'
FLOW_LOG_EXTENSION = 'FLOW_LOG'

# The alias of the extension.
ALIAS = 'flow-log'

# The description of the extension.
DESCRIPTION = "Flow Log Extension"

# A timestamp of when the extension was introduced.
UPDATED_TIMESTAMP = "2024-07-17T10:00:00-08:08"

# The name of the resource.
FLOW_LOG = 'flow_log'

# The plural for the resource.
FLOW_LOGS = 'flow_logs'

RESOURCE_ATTRIBUTE_MAP = {
    FLOW_LOGS: {
        'id': {'allow_post': False,
               'allow_put': False,
               'validate': {'type:uuid': None},
               'is_visible': True,
               'is_filter': True,
               'is_sort_key': True,
               'primary_key': True},
        'name': {'allow_post': True,
                 'allow_put': True,
                 'validate': {'type:string': db_const.NAME_FIELD_SIZE},
                 'is_sort_key': True,
                 'is_visible': True,
                 'is_filter': True,
                 'default': ''},
        'project_id': {'allow_post': True,
                       'allow_put': False,
                       'validate': {
                           'type:string': db_const.PROJECT_ID_FIELD_SIZE},
                       'required_by_policy': True,
                       'is_visible': True},
        'collection_interval': {'allow_post': True,
                                'allow_put': False,
                                'convert_to': converters.convert_to_int,
                                'validate': {'type:values': [1, 5, 10]},
                                'default': 10,
                                'is_visible': True,
                                'is_filter': True},
        # 'priority': {'allow_post': True,
        #              'allow_put': False,
        #              'convert_to': converters.convert_to_int,
        #              'validate': {'type:range': [1, 65535]},
        #              'default': 1,
        #              'is_filter': True,
        #              'is_visible': True},
        'enabled': {'allow_post': True,
                    'allow_put': True,
                    'default': False,
                    'convert_to': converters.convert_to_boolean,
                    'is_visible': True,
                    'is_filter': True},
        # 'resource_id': {'allow_post': True,
        #                 'allow_put': False,
        #                 'validate': {'type:uuid': None},
        #                 'is_visible': True,
        #                 'is_filter': True},
        # 'resource_type': {'allow_post': True,
        #                   'allow_put': False,
        #                   'is_visible': True,
        #                   'is_filter': True,
        #                   'validate': {'type:values': ['port', 'network']}},
        'traffic_type': {'allow_post': True,
                         'allow_put': False,
                         'is_visible': True,
                         'is_filter': True,
                         'default': 'all',
                         'validate': {
                             'type:values': ['all', 'accept', 'reject']}},
    },
    port.COLLECTION_NAME: {
        log_const.FLOW_LOG_ID: {
            'allow_post': True,
            'allow_put': True,
            'is_visible': True,
            'default': None,
            'validate': {'type:uuid_or_none': None}
        }
    },
    network.COLLECTION_NAME: {
        log_const.FLOW_LOG_ID: {
            'allow_post': True,
            'allow_put': True,
            'is_visible': True,
            'default': None,
            'validate': {'type:uuid_or_none': None}
        }
    }
}

# The list of required extensions.
REQUIRED_EXTENSIONS = []

# Whether or not this extension is simply signaling behavior to the user
# or it actively modifies the attribute map.
IS_SHIM_EXTENSION = False

# Whether the extension is marking the adoption of standardattr model for
# legacy resources, or introducing new standardattr attributes. False or
# None if the standardattr model is adopted since the introduction of
# resource extension.
# If this is True, the alias for the extension should be prefixed with
# 'standard-attr-'.
IS_STANDARD_ATTR_EXTENSION = False

# The subresource attribute map for the extension. It adds child resources
# to main extension's resource. The subresource map must have a parent and
# a parameters entry. If an extension does not need such a map, None can
# be specified (mandatory).
SUB_RESOURCE_ATTRIBUTE_MAP = {}

# The action map: it associates verbs with methods to be performed on
# the API resource.
ACTION_MAP = {}

# The list of optional extensions.
OPTIONAL_EXTENSIONS = []

# The action status.
ACTION_STATUS = {}
