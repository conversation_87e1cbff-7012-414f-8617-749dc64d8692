#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock

import netaddr
from neutron_lib import context
from oslo_config import cfg

from neutron.agent.common import ovs_lib
from neutron.agent.l2.extensions import lbaas_dscp
from neutron.conf.agent import common as agent_config
from neutron.objects.ports import Port as port_obj
from neutron.objects.ports import PortCloudAttribute as port_attrs_obj
from neutron.plugins.ml2.drivers.openvswitch.agent \
    import ovs_agent_extension_api as ovs_ext_api
from neutron.tests import base


PORT_DETAIL = {
    "port_id": "fc4f30da-bab0-41eb-9f6e-f179b632fac1",
    "admin_state_up": True,
    "network_id": "net1",
    "fixed_ips": [
        {"subnet_id": "subnet1",
         "ip_address": "**************"},
        {"subnet_id": "subnet2",
         "ip_address": "************"}
    ],
    "vif_port": ovs_lib.VifPort(port_name="port1", ofport=1,
                                vif_id="fc4f30da-bab0-41eb-9f6e-f179b632fac1",
                                vif_mac=netaddr.EUI("aa:bb:cc:dd:ee:ff"),
                                switch="br-int"),
    "mac_address": netaddr.EUI("aa:bb:cc:dd:ee:ff"),
}


class LbaasDscpAgentExtensionTestCase(base.BaseTestCase):

    def setUp(self):
        super(LbaasDscpAgentExtensionTestCase, self).setUp()
        agent_config.register_agent_state_opts_helper(cfg.CONF)
        self.context = context.get_admin_context()
        self.int_br = mock.Mock()
        self.tun_br = mock.Mock()
        self.vlan_br = mock.Mock()
        self.plugin_rpc = mock.Mock()
        self.remote_resource_cache = mock.Mock()
        self.plugin_rpc.remote_resource_cache = self.remote_resource_cache
        self.lb_dscp_ext = lbaas_dscp.LbaasDscpAgentExtension()
        self.int_ofport = 200
        self.phys_ofport = 100
        self.agent_api = ovs_ext_api.OVSAgentExtensionAPI(
            self.int_br,
            self.tun_br,
            plugin_rpc=self.plugin_rpc,
            phys_brs={"vlan": self.vlan_br},
            phys_ofports={"vlan": self.phys_ofport},
            bridge_mappings={"vlan": "br-provider"})
        self.lb_dscp_ext.consume_api(self.agent_api)
        self.lb_dscp_ext.initialize(None, None)
        self.lb_dscp_ext.int_br = self.int_br
        self.port_cloud_attrs = port_attrs_obj(
            id="fc4f30da-bab0-41eb-9f6e-f179b632fac1",
            cloud_attrs={"key1": "value1",
                         "dscp_learn": [{"tos": "44",
                                        "protocol": "tcp"},
                                        {"tos": "64",
                                         "protocol": "udp"}]
                         }
        )
        self.port = port_obj(
            id="fc4f30da-bab0-41eb-9f6e-f179b632fac1",
            name="port1",
            network="net1",
            mac_address=netaddr.EUI("aa:bb:cc:dd:ee:ff"),
            admin_state_up=True,
            status="ACTIVE",
            cloud_attributes=self.port_cloud_attrs
        )
        cfg.CONF.set_override('firewall_driver', 'openvswitch',
                              group='SECURITYGROUP')
        cfg.CONF.set_override('enable_lbaas_dscp', True, 'AGENT')

    def test_handle_port_create_dscp_attrs_ovs_fw(self):
        self._test_handle_port_create_dscp_attrs()

    def test_handle_port_update_dscp_attrs_ovs_fw(self):
        self._test_handle_port_update_dscp_attrs()

    def test_delete_port_ovs_fw(self):
        self._test_delete_port_with_dscp_attrs()

    def test_process_dscp_learn_flow_ovs_fw(self):
        with mock.patch.object(self.lb_dscp_ext.ext_api, 'get_port_ip',
                               return_value=[("**************", "subnet1")]):
            dscp_learn_attrs = {"tos": "44", "protocol": "tcp"}
            self.int_br.get_value_from_other_config = mock.Mock()
            self.lb_dscp_ext.process_dscp_learn_flow(
                PORT_DETAIL, dscp_learn_attrs)
            learn_action = 'learn(cookie=%s,table=70,priority=100,' \
                           'idle_timeout=900,hard_timeout=1800,' \
                           'eth_type=0x0800,ip_proto=6,reg5=0x1,' \
                           'NXM_OF_IP_SRC[]=NXM_OF_IP_DST[],' \
                           'NXM_OF_IP_DST[]=NXM_OF_IP_SRC[],' \
                           'NXM_OF_TCP_SRC[]=NXM_OF_TCP_DST[],' \
                           'NXM_OF_TCP_DST[]=NXM_OF_TCP_SRC[],' \
                           'load:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[])' % \
                           self.int_br.default_cookie
            self.assertEqual(self.int_br.add_flow.call_args_list, [
                mock.call(actions=learn_action, priority=20,
                          proto='eth_type=0x0800,ip_proto=6',
                          reg5='0x1', dl_dst=netaddr.EUI("aa:bb:cc:dd:ee:ff"),
                          nw_tos='44', table=87)])

    def test_delete_dscp_learn_flow_ovs_fw(self):
        with mock.patch.object(self.lb_dscp_ext.ext_api, 'get_port_ip',
                               return_value=[("**************", "subnet1")]):
            self.lb_dscp_ext.delete_dscp_learn_flow(PORT_DETAIL)
            self.assertEqual(self.int_br.delete_flows.call_args_list, [
                mock.call(table=87, dl_dst=netaddr.EUI("aa:bb:cc:dd:ee:ff")),
                mock.call(table=70, nw_src="**************")])

    def _test_handle_port_create_dscp_attrs(self):
        with mock.patch.object(self.lb_dscp_ext.ext_api, "get_port_info",
                               return_value=self.port) as get_port_info,\
                mock.patch.object(self.lb_dscp_ext,
                                  "get_port_dscp_learn_attrs_from_cache",
                                  return_value=None) as get_dscp_attrs,\
                mock.patch.object(self.lb_dscp_ext,
                                  "process_dscp_learn_flow") as process_dscp, \
                mock.patch.object(self.lb_dscp_ext,
                                  "set_port_dscp_learn_attrs_cache"
                                  ) as set_dscp_cache:
            attrs = self.port_cloud_attrs
            dscp_attrs = attrs['cloud_attrs']['dscp_learn']
            self.lb_dscp_ext.handle_port(self.context, PORT_DETAIL)
            get_port_info.assert_called_once_with(PORT_DETAIL['port_id'])
            get_dscp_attrs.assert_called_once_with(PORT_DETAIL['port_id'])
            process_dscp.assert_has_calls([
                mock.call(PORT_DETAIL, dscp_attrs[0]),
                mock.call(PORT_DETAIL, dscp_attrs[1])])
            dscp_learn_attrs_cache = {"port_id": PORT_DETAIL['port_id'],
                                      "network_id": PORT_DETAIL['network_id'],
                                      "fixed_ips": PORT_DETAIL['fixed_ips'],
                                      "vif_port": PORT_DETAIL['vif_port'],
                                      "dscp_learn": dscp_attrs}
            set_dscp_cache.assert_called_with(PORT_DETAIL['port_id'],
                                              dscp_learn_attrs_cache)

    def _test_handle_port_update_dscp_attrs(self):
        old_dscp_attrs = {"port_id": "fc4f30da-bab0-41eb-9f6e-f179b632fac1",
                          "dscp_learn": {"tos": "11",
                                         "protocol": "tcp"}}
        with mock.patch.object(self.lb_dscp_ext.ext_api, "get_port_info",
                               return_value=self.port) as get_port_info,\
                mock.patch.object(self.lb_dscp_ext,
                                  "get_port_dscp_learn_attrs_from_cache",
                                  return_value=old_dscp_attrs
                                  ) as get_dscp_attrs,\
                mock.patch.object(self.lb_dscp_ext,
                                  "delete_dscp_learn_flow") as delete_dscp, \
                mock.patch.object(self.lb_dscp_ext,
                                  "process_dscp_learn_flow") as process_dscp:
            attrs = self.port_cloud_attrs
            dscp_attrs = attrs['cloud_attrs']['dscp_learn']
            self.lb_dscp_ext.handle_port(self.context, PORT_DETAIL)
            get_port_info.assert_called_once_with(PORT_DETAIL['port_id'])
            get_dscp_attrs.assert_called_once_with(PORT_DETAIL['port_id'])
            delete_dscp.assert_called_once_with(PORT_DETAIL)
            process_dscp.assert_has_calls([
                mock.call(PORT_DETAIL, dscp_attrs[0]),
                mock.call(PORT_DETAIL, dscp_attrs[1])])

    def _test_delete_port_with_dscp_attrs(self):
        old_dscp_attrs = {"port_id": "fc4f30da-bab0-41eb-9f6e-f179b632fac1",
                          "dscp_learn": {"tos": "44",
                                         "protocol": "udp"}}
        with mock.patch.object(self.lb_dscp_ext,
                               "get_port_dscp_learn_attrs_from_cache",
                               return_value=old_dscp_attrs
                               ) as get_dscp_attrs,\
                mock.patch.object(self.lb_dscp_ext,
                                  "delete_dscp_learn_flow") as delete_dscp:
            self.lb_dscp_ext.delete_port(self.context, PORT_DETAIL)
            get_dscp_attrs.assert_called_once_with(PORT_DETAIL['port_id'])
            delete_dscp.assert_called_once_with(PORT_DETAIL)

    def test_handle_port_create_dscp_attrs_stateless_fw(self):
        cfg.CONF.set_override('firewall_driver', 'openvswitch_stateless',
                              group='SECURITYGROUP')
        self._test_handle_port_create_dscp_attrs()

    def test_handle_port_update_dscp_attrs_stateless_fw(self):
        cfg.CONF.set_override('firewall_driver', 'openvswitch_stateless',
                              group='SECURITYGROUP')
        self._test_handle_port_update_dscp_attrs()

    def test_delete_port_stateless_fw(self):
        cfg.CONF.set_override('firewall_driver', 'openvswitch_stateless',
                              group='SECURITYGROUP')
        self._test_delete_port_with_dscp_attrs()

    def test_process_dscp_learn_flow_stateless_fw(self):
        cfg.CONF.set_override('firewall_driver', 'openvswitch_stateless',
                              group='SECURITYGROUP')
        with mock.patch.object(self.lb_dscp_ext.ext_api, 'get_port_ip',
                               return_value=[("**************", "subnet1")]):
            dscp_learn_attrs = {"tos": "44", "protocol": "tcp"}
            self.int_br.get_value_from_other_config = mock.Mock(
                return_value=10)
            self.lb_dscp_ext.process_dscp_learn_flow(
                PORT_DETAIL, dscp_learn_attrs)
            learn_action = 'learn(cookie=%s,table=70,priority=100,' \
                           'idle_timeout=900,hard_timeout=1800,' \
                           'NXM_OF_VLAN_TCI[0..11],' \
                           'eth_type=0x0800,ip_proto=6,' \
                           'NXM_OF_IP_SRC[]=NXM_OF_IP_DST[],' \
                           'NXM_OF_IP_DST[]=NXM_OF_IP_SRC[],' \
                           'NXM_OF_TCP_SRC[]=NXM_OF_TCP_DST[],' \
                           'NXM_OF_TCP_DST[]=NXM_OF_TCP_SRC[],' \
                           'load:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[])' % \
                           self.int_br.default_cookie
            self.assertEqual(self.int_br.add_flow.call_args_list, [
                mock.call(actions=learn_action, priority=20,
                          proto='eth_type=0x0800,ip_proto=6',
                          dl_vlan=10,
                          dl_dst=netaddr.EUI("aa:bb:cc:dd:ee:ff"),
                          nw_tos='44', table=87)])

    def test_delete_dscp_learn_flow_stateless_fw(self):
        cfg.CONF.set_override('firewall_driver', 'openvswitch_stateless',
                              group='SECURITYGROUP')
        with mock.patch.object(self.lb_dscp_ext.ext_api, 'get_port_ip',
                               return_value=[("**************", "subnet1")]):
            self.int_br.get_value_from_other_config = mock.Mock(
                return_value=10)
            self.lb_dscp_ext.delete_dscp_learn_flow(PORT_DETAIL)
            self.assertEqual(self.int_br.delete_flows.call_args_list, [
                mock.call(table=87,
                          dl_dst=netaddr.EUI("aa:bb:cc:dd:ee:ff")),
                mock.call(table=70, nw_src="**************")])

    def test_handle_port_with_disable_config(self):
        cfg.CONF.set_override('enable_lbaas_dscp', False, 'AGENT')
        self.lb_dscp_ext.ext_api.get_port_ip = mock.Mock()
        self.lb_dscp_ext.handle_port(self.context, PORT_DETAIL)
        self.lb_dscp_ext.ext_api.get_port_ip.assert_not_called()

    def test_delete_port_with_disable_config(self):
        cfg.CONF.set_override('enable_lbaas_dscp', False, 'AGENT')
        self.lb_dscp_ext.get_port_dscp_learn_attrs_from_cache = mock.Mock()
        self.lb_dscp_ext.delete_port(self.context, PORT_DETAIL)
        self.lb_dscp_ext.get_port_dscp_learn_attrs_from_cache.\
            assert_not_called()
