#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron.db.models import elastic_snat as models
from neutron.objects import base
from neutron.objects import common_types
from neutron.objects import router
from neutron.objects import subnet as subnet_obj
from oslo_versionedobjects import fields as obj_fields

FIELDS_NOT_SUPPORT_FILTER = ['internal_cidrs', 'subnets']


@base.NeutronObjectRegistry.register
class ElasticSnatRule(base.NeutronDbObject):
    # Version 1.0: Initial version
    # Version 1.1: Modify unique constraints
    VERSION = '1.1'

    db_model = models.ElasticSnatRule

    primary_keys = ['id']

    fields = {
        'id': common_types.UUIDField(),
        'elastic_snat_id': common_types.UUIDField(nullable=False),
        'router_id': common_types.UUIDField(nullable=False),
        'floatingip_id': common_types.UUIDField(nullable=False),
        'internal_cidr': obj_fields.StringField(nullable=True),
        'subnet_id': obj_fields.UUIDField(nullable=True)
    }


@base.NeutronObjectRegistry.register
class ElasticSnat(base.NeutronDbObject):
    # Version 1.0: Initial version
    # Version 1.1: Add missing project_id
    VERSION = '1.1'

    db_model = models.ElasticSnat

    primary_keys = ['id']
    foreign_keys = {'FloatingIP': {'floatingip_id': 'id'},
                    'Router': {'router_id': 'id'},
                    'Port': {'gateway_port_id': 'id'}}

    fields = {
        'id': common_types.UUIDField(),
        'name': obj_fields.StringField(nullable=True),
        'router_id': common_types.UUIDField(nullable=False),
        'gateway_port_id': common_types.UUIDField(nullable=False),
        'floatingip_id': common_types.UUIDField(nullable=False),
        'internal_cidrs': obj_fields.ListOfStringsField(nullable=True),
        'subnets': obj_fields.ListOfStringsField(nullable=True),
        'project_id': obj_fields.StringField(),
        'floating_ip_address': obj_fields.IPV4AddressField()
    }

    synthetic_fields = ['floating_ip_address',
                        'subnets',
                        'internal_cidrs']
    fields_no_update = {'id', 'router_id', 'project_id'}

    def _set_subnet_rules(self, subnets):
        ElasticSnatRule.delete_objects(self.obj_context,
                                       elastic_snat_id=self.id)
        if subnets:
            for subnet in subnets:
                s = subnet_obj.Subnet.get_object(self.obj_context, id=subnet)
                ElasticSnatRule(
                    self.obj_context,
                    elastic_snat_id=self.id,
                    router_id=self.router_id,
                    floatingip_id=self.floatingip_id,
                    internal_cidr=str(s.cidr),
                    subnet_id=subnet).create()
        self.subnets = subnets
        self.internal_cidrs = []
        self.obj_reset_changes(['subnets', 'internal_cidrs'])

    def _set_internal_cidr_rules(self, internal_cidrs):
        ElasticSnatRule.delete_objects(self.obj_context,
                                       elastic_snat_id=self.id)
        if internal_cidrs:
            for internal_cidr in internal_cidrs:
                ElasticSnatRule(
                    self.obj_context,
                    elastic_snat_id=self.id,
                    router_id=self.router_id,
                    floatingip_id=self.floatingip_id,
                    internal_cidr=internal_cidr).create()
        self.internal_cidrs = internal_cidrs
        self.subnets = []
        self.obj_reset_changes(['subnets', 'internal_cidrs'])

    def create(self):
        fields = self.obj_get_changes()
        with self.db_context_writer(self.obj_context):
            super(ElasticSnat, self).create()
            if fields.get('subnets', []):
                self._set_subnet_rules(fields['subnets'])

            if fields.get('internal_cidrs', []):
                self._set_internal_cidr_rules(fields['internal_cidrs'])

    def update(self):
        fields = self.obj_get_changes()
        with self.db_context_writer(self.obj_context):
            super(ElasticSnat, self).update()
            if 'subnets' in fields:
                self._set_subnet_rules(fields['subnets'])

            if 'internal_cidrs' in fields:
                self._set_internal_cidr_rules(fields['internal_cidrs'])

    def from_db_object(self, db_obj):
        super(ElasticSnat, self).from_db_object(db_obj)
        fip = router.FloatingIP.get_object(self.obj_context,
                                           id=self.floatingip_id)
        setattr(self, 'floating_ip_address', fip.floating_ip_address)
        self.obj_reset_changes(['floating_ip_address'])

        rules = ElasticSnatRule.get_objects(self.obj_context,
                                            elastic_snat_id=self.id)
        subnets = [r.subnet_id for r in rules if r.subnet_id]
        setattr(self, 'subnets', subnets)
        self.obj_reset_changes(['subnets'])

        internal_cidrs = [r.internal_cidr for r in rules if r.internal_cidr]
        setattr(self, 'internal_cidrs', internal_cidrs)
        self.obj_reset_changes(['internal_cidrs'])

    @classmethod
    def modify_fields_from_db(cls, db_obj):
        result = super(ElasticSnat, cls).modify_fields_from_db(db_obj)
        if 'rules' in result:
            subnets = [r.subnet for r in result['rules'] if r.subnet]
            result['subnets'] = subnets
            internal_cidrs = [r.internal_cidr for r in
                              result['rules'] if r.internal_cidr]
            result['internal_cidrs'] = internal_cidrs
            del result['rules']
        return result

    @classmethod
    def modify_fields_to_db(cls, fields):
        result = super(ElasticSnat, cls).modify_fields_to_db(fields)
        if 'subnets' in result:
            del result['subnets']
        if 'internal_cidrs' in result:
            del result['internal_cidrs']
        return result
