#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.plugins import directory

from neutron.core_extensions import base
from neutron.db import api as db_api
from neutron.objects import flow_log as objects
from neutron.services.flow_log.common import constants as log_consts


class FlowLogCoreResourceExtension(base.CoreResourceExtension):

    @property
    def plugin_loaded(self):
        if not hasattr(self, '_plugin_loaded'):
            self._plugin_loaded = 'FLOW_LOG' in directory.get_plugins()
        return self._plugin_loaded

    def _update_port_flow_log(self, context, port, port_changes):
        old_flow_log = objects.FlowLog.get_port_flow_log(
            context.elevated(), port['id'])
        if old_flow_log:
            old_flow_log.detach_port(port['id'])

        flow_log_id = port_changes.get(log_consts.FLOW_LOG_ID)
        if flow_log_id is not None:
            flow_log = objects.FlowLog.get_flow_log_obj(context, flow_log_id)
            flow_log.attach_port(port['id'])
        port[log_consts.FLOW_LOG_ID] = flow_log_id

    def _create_network_flow_log(self, context, network, network_changes):
        flow_log_id = network_changes.get(log_consts.FLOW_LOG_ID)
        if not flow_log_id:
            return

        if flow_log_id is not None:
            flow_log = objects.FlowLog.get_flow_log_obj(context, flow_log_id)
            flow_log.attach_network(network['id'])
        network[log_consts.FLOW_LOG_ID] = flow_log_id

    def _update_network_flow_log(self, context, network, network_changes):
        old_flow_log = objects.FlowLog.get_network_flow_log(
            context.elevated(), network['id'])
        if old_flow_log:
            old_flow_log.detach_network(network['id'])

        flow_log_id = network_changes.get(log_consts.FLOW_LOG_ID)
        if flow_log_id is not None:
            flow_log = objects.FlowLog.get_flow_log_obj(context, flow_log_id)
            flow_log.attach_network(network['id'])
        network[log_consts.FLOW_LOG_ID] = flow_log_id

    def _exec(self, method_name, context, kwargs):
        with db_api.autonested_transaction(context.session):
            return getattr(self, method_name)(context=context, **kwargs)

    def process_fields(self, context, resource_type, event_type,
                       requested_resource, actual_resource):
        if (log_consts.FLOW_LOG_ID in requested_resource and
                self.plugin_loaded):
            method_name = ('_%(event)s_%(resource)s_flow_log' %
                           {'event': event_type, 'resource': resource_type})
            self._exec(method_name, context,
                       {resource_type: actual_resource,
                        "%s_changes" % resource_type: requested_resource})

    def extract_fields(self, resource_type, resource):
        if not self.plugin_loaded:
            return {}

        binding = resource['flow_log_binding']
        flow_log_id = binding[log_consts.FLOW_LOG_ID] if binding else None
        return {log_consts.FLOW_LOG_ID: flow_log_id}
