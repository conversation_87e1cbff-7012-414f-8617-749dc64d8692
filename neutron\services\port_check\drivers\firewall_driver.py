# Copyright (c) 2021 China Unicom Cloud Data Co.,Ltd.
# Copyright (c) 2019 - 2020 China Telecom Corporation
# All Rights Reserved.
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.callbacks import events as callbacks_events
from neutron_lib.callbacks import registry as callbacks_registry
from neutron_lib.callbacks import resources as callbacks_resources

from neutron.agent.linux.openvswitch_firewall import firewall as ovs_fw
from neutron.agent.linux.openvswitch_firewall import stateless_firewall


class OVSFirewallDriver(ovs_fw.OVSFirewallDriver):

    def __init__(self, integration_bridge):
        super(OVSFirewallDriver, self).__init__(integration_bridge)
        # subsciption is not needed:
        callbacks_registry.unsubscribe(
            self._init_firewall_callback,
            callbacks_resources.AGENT,
            callbacks_events.OVS_RESTARTED)

    @property
    def reports(self):
        return self.int_br.reports

    @staticmethod
    def initialize_bridge(int_br):
        return int_br


class OVSStatelessFirewallDriver(
        stateless_firewall.OVSStatelessFirewallDriver):

    def __init__(self, integration_bridge):
        super(OVSStatelessFirewallDriver, self).__init__(integration_bridge)
        self.int_br = integration_bridge

    @property
    def reports(self):
        return self.int_br.reports
