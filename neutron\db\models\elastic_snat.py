#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import sqlalchemy as sa
from sqlalchemy import orm

from neutron_lib.db import constants as db_const
from neutron_lib.db import model_base

from neutron.extensions import _elastic_snat as apidef


class ElasticSnat(model_base.BASEV2, model_base.HasId,
                  model_base.HasProject):

    __tablename__ = 'elastic_snats'

    name = sa.Column(sa.String(db_const.NAME_FIELD_SIZE))
    router_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
                          sa.<PERSON>('routers.id'),
                          nullable=False)
    gateway_port_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
                                sa.<PERSON>ey('ports.id'),
                                nullable=False)
    floatingip_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
                              sa.ForeignKey('floatingips.id'),
                              nullable=False)

    api_collections = [apidef.COLLECTION_NAME]

    __table_args__ = (
        sa.UniqueConstraint('floatingip_id',
                            name='uniq_elastic_snats0floatingip_id'),
    )


class ElasticSnatRule(model_base.BASEV2, model_base.HasId):

    __tablename__ = 'elastic_snat_rules'

    elastic_snat_id = sa.Column(
        sa.String(db_const.UUID_FIELD_SIZE),
        sa.ForeignKey('elastic_snats.id', ondelete="CASCADE"),
        nullable=False)
    router_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
                          nullable=False)
    floatingip_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
                              nullable=False)
    internal_cidr = sa.Column(sa.String(64), nullable=True)
    subnet_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
                          nullable=True)

    elastic_snat = orm.relationship(ElasticSnat, load_on_pending=True,
                                    backref=orm.backref("rules",
                                                        lazy='subquery',
                                                        cascade='delete'))

    __table_args__ = (
        sa.UniqueConstraint('floatingip_id', 'subnet_id',
                            name='uniq_elastic_snat_rules0'
                                 'floatingip_id0subnet_id'),
        sa.UniqueConstraint('floatingip_id', 'internal_cidr',
                            name='uniq_elastic_snat_rules0floatingip_id0'
                                 'internal_cidr'),
    )
