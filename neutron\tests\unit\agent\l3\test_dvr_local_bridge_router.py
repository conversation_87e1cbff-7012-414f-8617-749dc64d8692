# Copyright (c) 2019 Intel Corporation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import functools

import mock

from oslo_serialization.jsonutils import netaddr
from oslo_utils import importutils
from ryu.lib.packet import arp
from ryu.lib.packet import ether_types
from ryu.lib.packet import icmp as icmpv4
from ryu.lib.packet import in_proto

from neutron.agent.l2.extensions.communicate_tunnel import comm_sock
from neutron.agent.l3 import dvr_local_bridge_router as dvr_bridge
from neutron.agent.l3.openflow_utils.native import dvr_bridge \
    as native_dvr_bridge
from neutron.common import constants as l3_constants
from neutron.conf.agent import common as agent_config
from neutron.conf.agent.l3 import config as l3_config
from neutron.tests import base
from oslo_config import cfg
from oslo_utils import uuidutils

_uuid = uuidutils.generate_uuid
HOSTNAME = 'myhost'
INTERNAL_DEV_PREFIX = 'qr-'
EXTERNAL_DEV_PREFIX = 'qg-'
FIP_NS_PREFIX = 'fip-'
FIP_EXT_DEV_PREFIX = 'fg-'


class TestDvrLocalBridgeRouter(base.BaseTestCase):
    def setUp(self):
        super(TestDvrLocalBridgeRouter, self).setUp()
        conn_patcher = mock.patch(
            'neutron.agent.ovsdb.impl_idl._connection')
        conn_patcher.start()
        self.conf = agent_config.setup_conf()
        self.conf.register_opts(agent_config.AGENT_STATE_OPTS, 'AGENT')
        l3_config.register_l3_agent_config_opts(l3_config.OPTS, self.conf)
        l3_config.register_l3_agent_ovs_opts(l3_config.ovs_opts, self.conf)
        agent_config.register_interface_opts(self.conf)
        ovs_opt = cfg.StrOpt('foo')
        self.conf.register_opt(ovs_opt, group='OVS')
        self.router_id = 'myrouter'
        self.port = {
            'network_id': _uuid(),
            'device_owner': 'network:router_interface_distributed',
            'fixed_ips': [{'ip_address': '********',
                           'subnet_id': 'my-sub2'}],
            'id': 'port_foo',
            'mac_address': 'fa:16:3e:35:89:42',
            'subnets': [{'cidr': '10.0.0.0/26',
                         'id': 'my-sub2'}]
        }
        bridge_class = importutils.import_class(
            "neutron.agent.l3.openflow_utils.ofctl.dvr_bridge.L3AgentBridge")
        self.setup_bridge_mock("test", bridge_class)

    def setup_bridge_mock(self, name, cls):
        self.br = cls(name)
        mock_add_flow = mock.patch.object(self.br, 'add_flow').start()
        mock_mod_flow = mock.patch.object(self.br, 'mod_flow').start()
        mock_delete_flows = mock.patch.object(self.br, 'delete_flows').start()
        self.mock = mock.Mock()
        self.mock.attach_mock(mock_add_flow, 'add_flow')
        self.mock.attach_mock(mock_mod_flow, 'mod_flow')
        self.mock.attach_mock(mock_delete_flows, 'delete_flows')

    @mock.patch('neutron.agent.common.ovs_lib.OVSBridge')
    def _create_router(self, router=None, **kwargs):
        router = {
            '_floatingips': [
                {'id': 'my_fip1',
                 'host': 'some-other-host',
                 'floating_ip_address': '***********',
                 'fixed_ip_address': '*********0',
                 'floating_network_id': 'my_fnw1',
                 'port_id': 'my_port1'}],
            '_interfaces': [
                {'fixed_ips': [
                    {'ip_address': '*********',
                     'prefixlen': 24,
                     'subnet_id': 'sub-2'}],
                 'device_owner':
                 'network:router_interface_distributed',
                 'admin_state_up': True,
                 'id': 'intf-2',
                 'mac_address': 'aa:bb:cc:dd:ee:22',
                 'network_id': 'net-2',
                 'subnets': [
                     {'cidr': '*********/24',
                      'gateway_ip': '*********',
                      'id': 'sub-2',
                      'ipv6_ra_mode': None}]}],
            'id': '3a220dc3-49af-47c2-8fa3-d2397f8568e4',
            'gw_port': {
                'fixed_ips': [{'ip_address': '************',
                               'prefixlen': 24,
                               'subnet_id': 'my-ext-sub1'}],
                'id': 'my_ex_gw_port',
                'mac_address': 'aa:bb:cc:dd:ee:ff',
                'subnets': [{'id': _uuid(),
                             'cidr': '**********/24',
                             'gateway_ip': None}],
                'network_id': 'ext-net-id'},
            'port_allowed_pairs': [
                ('************',
                 'aa:cc:dd:ff:bb:ee',
                 '3a220dc3-49af-47c2-8fa3-d2397f8568e4')],
            '_floatingip_agent_interfaces': [
                {'id': 'fip_agent_id_01',
                 'network_id': 'ext-net-id'}
            ],
            'routes': []}

        kwargs['router_id'] = self.router_id
        kwargs['router'] = router
        kwargs['use_ipv6'] = False
        kwargs['agent_conf'] = self.conf
        agent = mock.MagicMock()
        bridge_router = dvr_bridge.DvrLocalBridgeRouter(
            HOSTNAME, agent, **kwargs)
        bridge_router.br = mock.MagicMock()
        bridge_router.int_br = mock.MagicMock()
        bridge_router.ex_br = mock.MagicMock()
        return bridge_router

    def test_dvr_bridge_initialize(self):
        agent = mock.MagicMock()
        router_id = "uuid"
        router = mock.MagicMock()
        agent_conf = mock.MagicMock()
        agent_conf.ovs_integration_bridge = "br-int"
        with mock.patch('neutron.agent.common.ovs_lib.OVSBridge') as ovs_br:
            ovs_br.create = mock.MagicMock()
            ovs_br.setup_controllers = mock.MagicMock()
            ovs_br.init_bridge_flows = mock.MagicMock()
            bridge_router = dvr_bridge.DvrLocalBridgeRouter(
                HOSTNAME, agent, router_id, router, agent_conf)
            bridge_router.initialize()
            self.assertEqual(ovs_br.call_args_list, [
                mock.call("br-int")])
            self.assertEqual(bridge_router.br.create.call_args_list, [
                mock.call(secure_mode=True)
            ])
            self.assertEqual(bridge_router.br.setup_controllers.call_args_list,
                             [mock.call(agent_conf)]
                             )
            self.assertEqual(bridge_router.br.init_bridge_flows.call_args_list,
                             [mock.call()])

    def test_dvr_bridge_delete(self):
        agent = mock.MagicMock()
        router_id = "uuid-123466789"
        router = mock.MagicMock()
        agent_conf = mock.MagicMock()
        agent_conf.ovs_integration_bridge = "br-int"
        bridge_router = dvr_bridge.DvrLocalBridgeRouter(
            HOSTNAME, agent, router_id, router, agent_conf)
        bridge_router.process_delete = mock.MagicMock()
        bridge_router.disable_radvd = mock.MagicMock()
        bridge_router.br = mock.MagicMock()
        bridge_router.delete()
        self.assertEqual(bridge_router.process_delete.call_args_list,
                         [mock.call()])
        self.assertEqual(bridge_router.disable_radvd.call_args_list,
                         [mock.call()])
        self.assertEqual(bridge_router.br.destroy.call_args_list,
                         [mock.call])

    def test_br_init_bridge_flows(self):
        self.br.init_bridge_flows()

        self.assertEqual(self.br.delete_flows.call_args_list,
                         [mock.call(cookie="0/0"),
                          mock.call(table=l3_constants.DVR_BRIDGE_INPUT_TABLE,
                                    cookie=0)])

        self.assertEqual(self.br.add_flow.call_args_list, [
            mock.call(
                table=l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE,
                priority=1, actions="NORMAL"),
            mock.call(
                table=l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE,
                priority=1, actions="DROP"),
            mock.call(
                table=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
                priority=1,
                actions=(
                    "resubmit(,%s)" % l3_constants.DVR_BRIDGE_FIP_AD_PORTS)),
            mock.call(
                table=l3_constants.DVR_BRIDGE_FIP_AD_PORTS,
                priority=1,
                actions="resubmit(,%s)" % l3_constants.DVR_BRIDGE_FIP_QOS),
            mock.call(
                table=l3_constants.DVR_BRIDGE_FIP_QOS,
                priority=1,
                actions="resubmit(,%s)" % l3_constants.DVR_BRIDGE_FIP_NAT),
            mock.call(
                table=l3_constants.DVR_BRIDGE_FIP_NAT,
                priority=1,
                actions=(
                    "resubmit(,%s)" % l3_constants.DVR_BRIDGE_ACL)),
            mock.call(
                table=l3_constants.DVR_BRIDGE_ACL,
                priority=1,
                actions=(
                    "resubmit(,%s)" % l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE)
            ),
            mock.call(
                table=l3_constants.DVR_BRIDGE_PRE_SNAT_ACL,
                priority=1,
                actions=(
                    "resubmit(,%s)" % l3_constants.DVR_BRIDGE_LOCAL_SNAT_TABLE
                ))
        ])

    def test_internal_network_added(self):
        ri = self._create_router()
        mock.patch.object(ri.br, 'install_router_interface_flows').start()
        mock.patch.object(ri, '_set_subnet_arp_info').start()
        attrs = [('type', 'patch'),
                 ('options', {'peer': 'intpport_foo'}),
                 ('external_ids', {'iface-status': 'active',
                                   'attached-mac': 'fa:16:3e:35:89:42',
                                   'iface-id': 'port_foo'})]
        ri.internal_network_added(self.port)

        # generate_router_interface_flows should be called the same number of
        # as fixed_ips in ports
        self.assertEqual(1, ri.br.install_router_interface_flows.call_count)

        # make sure patch port was added once on the integration bridge
        ri.int_br.add_port.assert_called_once_with('qr-port_foo', *attrs)

        # make sure patch port was added once on dvr bridge
        ri.br.add_patch_port.assert_called_once_with('intpport_foo',
                                                     'qr-port_foo')

        # make sure _set_subnet_arp_info called the same number of times as
        # there are subnets in port["subnets"]
        self.assertEqual(1, ri._set_subnet_arp_info.call_count)

    def test_internal_network_removed(self):
        ri = self._create_router()
        mock.patch.object(ri, '_set_subnet_arp_info').start()
        ri.internal_network_removed(self.port)

        ri.br.delete_port.assert_called_once_with('intpport_foo')
        ri.int_br.delete_port.assert_called_once_with('qr-port_foo')
        self.assertEqual(1, ri.br.remove_router_interface_ip_flows.call_count)

    def test_subnet_update(self):
        ri = self._create_router()
        ri.inf_ip_of_ports = {}
        ri.inf_ip_of_ports['*********'] = 1
        ri.inf_ip_of_ports['**********'] = 2
        ri.inf_mac_addresses = {}
        ri.inf_mac_addresses['**********'] = 'aa:bb:cc:dd:ee:11'
        ri.inf_mac_addresses['*********'] = 'aa:bb:cc:dd:ee:22'
        ri.agent.router_info = {}
        ri.agent.router_info['myrouter1'] = ri
        subnet = {'cidr': '*********/24',
                  'gateway_ip': '*********',
                  'id': 'sub-2',
                  'ipv6_ra_mode': None}
        router_ips = ['*********']
        mac_address = 'fa:16:3e:fc:ee:91'
        next_hop_table = l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE
        int_output_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE
        ri.subnet_update(subnet, router_ips, mac_address, source_router=ri)
        self.assertEqual(ri.br.install_route_goto.call_count, 3)
        ri.br.install_route_goto.assert_has_calls(
            [mock.call(next_hop_table, int_output_table, 100,
                       'aa:bb:cc:dd:ee:22', 'fa:16:3e:fc:ee:91',
                       '*********', 4),
             mock.call(next_hop_table, int_output_table, 100,
                       'aa:bb:cc:dd:ee:22', 'aa:bb:cc:dd:ee:22',
                       '*********', 4),
            mock.call(next_hop_table, int_output_table, 100,
                      'aa:bb:cc:dd:ee:22', 'aa:bb:cc:dd:ee:11',
                      '**********', 4)], any_order=True)

    def test_update_routing_table(self):
        ri = self._create_router()
        ri.inf_ip_of_ports = {}
        ri.inf_ip_of_ports['*********'] = 1
        ri.inf_ip_of_ports['**********'] = 2
        ri.inf_mac_addresses = {}
        ri.inf_mac_addresses['**********'] = 'aa:bb:cc:dd:ee:11'
        ri.inf_mac_addresses['*********'] = 'aa:bb:cc:dd:ee:22'
        ri.agent.router_info = {}
        ri.agent.router_info['myrouter1'] = ri
        route1 = {'destination': '*********/24', 'nexthop': '**********'}
        test_ports = [{'mac_address': 'aa:bb:cc:dd:ee:11',
                       'fixed_ips': [{'ip_address': '**********',
                                      'prefixlen': 24,
                                      'subnet_id': 'mysubnet1'}]}]

        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        ouput_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE
        with mock.patch.object(ri.agent, 'get_ports_by_subnet',
                               return_value=test_ports):
            ri.update_routing_table('replace', route1)
        self.assertEqual(ri.br.install_route_goto.call_count, 1)
        self.assertEqual(ri.br.install_route_goto.call_args_list, [
            mock.call(input_table, ouput_table, 250,
                      'aa:bb:cc:dd:ee:22', 'aa:bb:cc:dd:ee:11',
                      '*********/24', 4)])

        with mock.patch.object(ri.agent, 'get_ports_by_subnet',
                               return_value=test_ports):
            ri.update_routing_table('delete', route1)
        self.assertEqual(ri.br.remove_route_goto.call_args_list, [
            mock.call(input_table, '*********/24', 4)])

    def test_routes_updated(self):
        ri = self._create_router()
        old_routes = [{'destination': '*********/24', 'nexthop': '**********'}]
        new_routes = [{'destination': '*********/24', 'nexthop': '*********'}]

        with mock.patch.object(ri, 'update_routing_table'):
            ri.routes_updated(old_routes, new_routes)
            self.assertEqual(ri.update_routing_table.call_args_list, [
                mock.call('replace', {'nexthop': '*********',
                                      'destination': '*********/24'}),
                mock.call('delete', {'nexthop': '**********',
                                     'destination': '*********/24'})])

    def test_external_gateway_added(self):
        ex_gw_port = {'fixed_ips': [{'ip_address': '************',
                                     'prefixlen': 24,
                                     'subnet_id': 'mysubnet1'}],
                      'id': 'my_ex_gw_port',
                      'mac_address': 'aa:bb:cc:dd:ee:ff',
                      'subnets': [{'cidr': '**********/24',
                                   'dns_nameservers': [],
                                   'gateway_ip': '**********',
                                   'id': 'mysubnet1',
                                   'ipv6_ra_mode': None,
                                   'subnetpool_id': None}]}

        interface_name = 'qg-7c9e26d9-1e'
        ri = self._create_router()
        mock.patch.object(ri.br, 'install_learn_action_flows').start()
        mock.patch.object(ri.br, 'add_flow').start()
        with mock.patch.object(ri.br, 'get_port_ofport', return_value=3):
            ri.external_gateway_added(ex_gw_port, interface_name)
        attrs = [('type', 'patch'),
                 ('options', {'peer': 'ex-7c9e26d9-1e'}),
                 ('external_ids',
                  {'attached-mac': 'aa:bb:cc:dd:ee:ff',
                   'iface-id': 'my_ex_gw_port',
                   'iface-status': 'active'})]
        self.assertEqual(ri.br.add_patch_port.call_args_list,
                         [mock.call('ex-7c9e26d9-1e', 'qg-7c9e26d9-1e')])
        self.assertEqual(ri.int_br.add_port.call_args_list,
                         [mock.call('qg-7c9e26d9-1e', *attrs)])
        self.assertEqual(ri.br.install_learn_action_flows.call_args_list,
                         [mock.call(3)])

    def test_external_gateway_removed(self):
        ex_gw_port = {'fixed_ips': [{'ip_address': '************',
                                     'prefixlen': 24,
                                     'subnet_id': 'mysubnet1'}],
                      'id': 'my_ex_gw_port',
                      'mac_address': 'aa:bb:cc:dd:ee:ff',
                      'subnets': [{'cidr': '**********/24',
                                   'dns_nameservers': [],
                                   'gateway_ip': '**********',
                                   'id': 'mysubnet1',
                                   'ipv6_ra_mode': None,
                                   'subnetpool_id': None}]}
        ri = self._create_router()
        with mock.patch.object(ri.br, 'get_port_ofport', return_value=3):
            interface_name = 'qg-7c9e26d9-1e'
            ri.external_gateway_removed(ex_gw_port, interface_name)
        self.assertEqual(ri.br.delete_port.call_args_list, [
            mock.call('ex-7c9e26d9-1e')])
        self.assertEqual(ri.int_br.delete_port.call_args_list, [
            mock.call('qg-7c9e26d9-1e')])
        self.assertEqual(ri.br.remove_external_gateway_flows.call_args_list,
                         [mock.call(ex_gw_port)])

    def test_br_remove_external_gateway_flows(self):
        ex_gw_port = {'fixed_ips': [{'ip_address': '************',
                                     'prefixlen': 24,
                                     'subnet_id': 'mysubnet1'}],
                      'id': 'my_ex_gw_port',
                      'mac_address': 'aa:bb:cc:dd:ee:ff',
                      'subnets': [{'cidr': '**********/24',
                                   'dns_nameservers': [],
                                   'gateway_ip': '**********',
                                   'id': 'mysubnet1',
                                   'ipv6_ra_mode': None,
                                   'subnetpool_id': None}]}
        self.br.remove_external_gateway_flows(ex_gw_port)
        self.assertEqual(self.br.delete_flows.call_args_list, [
            mock.call(proto='arp', reg4='0x0', table=0),
            mock.call(proto='arp', table=70),
            mock.call(proto='icmp6', table=70),
            mock.call(dl_src='aa:bb:cc:dd:ee:ff', proto='ip'),
            mock.call(dl_src='aa:bb:cc:dd:ee:ff', proto='icmp'),
            mock.call(arp_tpa='************', proto='arp'),
            mock.call(nw_dst='************', proto='ip'),
            mock.call(table=80)])

    def test_process_floating_ip_addresses(self):
        ri = self._create_router()
        ri.inf_ip_of_ports = {}
        ri.inf_ip_of_ports['************'] = 3
        interface_name = 'qg-d9a6f930-dc'
        with mock.patch.object(ri, 'add_floating_ip'):
            with mock.patch.object(ri, 'remove_floating_ip'):
                ri.process_floating_ip_addresses(interface_name)
                ri.add_floating_ip.assert_called_once_with(
                    {'fixed_ip_address': '*********0', 'port_id': 'my_port1',
                     'floating_network_id': 'my_fnw1',
                     'floating_ip_address': '***********',
                     'id': 'my_fip1', 'host': 'some-other-host'},
                    'qg-d9a6f930-dc')

    def test_add_floating_ip(self):
        fip = {'fixed_ip_address': '***********',
               'floating_ip_address': '************',
               'floating_network_id': 'ext_net',
               'host': 'my_host',
               'id': 'my_fip',
               'port_details': {
                   'device_id': 'nova_id',
                   'device_owner': 'compute:nova',
                   'mac_address': 'fa:16:3e:22:27:96',
                   'network_id': 'my_net',
                   'status': 'ACTIVE'},
               'port_id': 'my_port1',
               'router_id': 'my_router',
               'admin_state_up': True,
               'denied_port_numbers': [1, 2]}
        ex_gw_port = {'fixed_ips': [{'ip_address': '************',
                                     'prefixlen': 24,
                                     'subnet_id': 'mysubnet1'}],
                      'id': 'my_ex_gw_port',
                      'network_id': 'ext_net',
                      'mac_address': 'aa:bb:cc:dd:ee:ff',
                      'subnets': [{'cidr': '**********/24',
                                   'dns_nameservers': [],
                                   'gateway_ip': '**********',
                                   'id': 'mysubnet1',
                                   'ipv6_ra_mode': None,
                                   'subnetpool_id': None}]}
        fg_port = {'fixed_ips': [{'ip_address': '************',
                                  'prefixlen': 24,
                                  'subnet_id': 'mysubnet1'}],
                   'id': 'my_fg_port',
                   'network_id': 'ext_net',
                   'mac_address': 'a1:b2:c3:d4:e5:f6',
                   'subnets': [{'cidr': '**********/24',
                                'dns_nameservers': [],
                                'gateway_ip': '**********',
                                'id': 'mysubnet1',
                                'ipv6_ra_mode': None,
                                'subnetpool_id': None}]}
        interface_name = 'qg-d9a6f930-dc'
        ri = self._create_router()
        ri.inf_ip_of_ports = {}
        ri.inf_ip_of_ports['************'] = 3
        ri.inf_ip_of_ports['************'] = 4
        mock.patch.object(ri, 'get_fip_agent_gateway',
                          return_value=fg_port).start()
        mock.patch.object(ri, 'get_ex_gw_port',
                          return_value=ex_gw_port).start()
        mock.patch.object(ri.agent.plugin_rpc, 'get_subnets_by_network',
                          return_value=[{'cidr': '**********/24',
                                         'gateway_ip': '**********'}]).start()
        fip['ex_cidr'] = '**********/24'
        ri.add_floating_ip(fip, interface_name)
        self.assertEqual(ri.br.install_fip_flows.call_args_list,
                         [mock.call(fip['floating_ip_address'],
                                    fip['fixed_ip_address'],
                                    '**********',
                                    fg_port['mac_address'],
                                    fip['port_details']['mac_address'],
                                    4,
                                    '**********/24',
                                    internal_cidr='')])
        self.assertEqual(ri.br.remove_drop_fip_admin_state.call_args_list,
                         [mock.call(fip['floating_ip_address'])])
        self.assertEqual(ri.br.remove_drop_fip_ad_ports.call_args_list,
                         [mock.call(fip['floating_ip_address'])])
        self.assertEqual(ri.br.install_drop_fip_ad_ports.call_args_list,
                         [mock.call(fip['floating_ip_address'], 1),
                          mock.call(fip['floating_ip_address'], 2)])

    def test_br_install_fip_flows(self):
        fip = {'fixed_ip_address': '***********',
               'floating_ip_address': '************',
               'floating_network_id': 'ext_net',
               'host': 'my_host',
               'id': 'my_fip',
               'port_details': {
                   'device_id': 'nova_id',
                   'device_owner': 'compute:nova',
                   'mac_address': 'fa:16:3e:22:27:96',
                   'network_id': 'my_net',
                   'status': 'ACTIVE'},
               'port_id': 'my_port1',
               'router_id': 'my_router',
               'admin_state_up': True,
               'denied_port_numbers': [1, 2]}
        self.br.install_fip_flows(fip['floating_ip_address'],
                                  fip['fixed_ip_address'],
                                  '**********',
                                  'a1:b2:c3:d4:e5:f6',
                                  fip['port_details']['mac_address'],
                                  4,
                                  '**********/24',
                                  internal_cidr='')
        self.br.add_flow.assert_has_calls(
            [mock.call(actions='mod_nw_dst:***********,resubmit(,45)',
                       nw_dst='************', priority=50,
                       proto='ip', table=40),
             mock.call(actions='load:0x4->NXM_NX_REG3[],' +
                               'load:0xac1804af->NXM_NX_REG4[],' +
                               'learn(priority=100, table=80,' +
                               'idle_timeout=30,' +
                               'eth_type=0x0800,' +
                               'NXM_OF_IP_DST[]=NXM_OF_IP_SRC[],' +
                               'NXM_OF_IP_SRC[]=NXM_NX_REG4[],' +
                               'load:0xac1804af->NXM_OF_IP_SRC[],' +
                               'load:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],' +
                               'load:0xa1b2c3d4e5f6->NXM_OF_ETH_SRC[],' +
                               'output:NXM_NX_REG3[]),resubmit(,5)',
                       nw_dst='************',
                       nw_src='**********/24',
                       priority=51,
                       proto='ip',
                       table=0),
             mock.call(actions='resubmit(,5)', nw_dst='************',
                       priority=50, proto='ip', table=0),
             mock.call(actions='resubmit(,21)',
                       nw_dst='************', priority=50,
                       proto='arp', table=0),
             mock.call(actions='resubmit(,4)',
                       nw_src='***********', priority=30, proto='ip',
                       table=0),
             mock.call(actions='resubmit(,50)',
                       nw_src='************', priority=60010,
                       proto='ip', table=45),
             mock.call(actions='resubmit(,80)',
                       nw_src='************', priority=80,
                       proto='ip', table=50),
             mock.call(
                 actions='mod_nw_src:************, '
                         'mod_dl_src:a1:b2:c3:d4:e5:f6, '
                         'dec_ttl, resubmit(,21)',
                 nw_src='***********', priority=30, proto='ip', table=5),
             mock.call(actions=(
                 'move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],'
                 'mod_dl_src:a1:b2:c3:d4:e5:f6,load:0x2->NXM_OF_ARP_OP[],'
                 'move:NXM_NX_ARP_SHA[]->NXM_NX_ARP_THA[],'
                 'move:NXM_OF_ARP_SPA[]->NXM_OF_ARP_TPA[],'
                 'set_field:************->arp_spa,'
                 'set_field:a1:b2:c3:d4:e5:f6->arp_sha,'
                 'resubmit(,60), resubmit(,70)'),
                 arp_op=1, nw_dst='************',
                 priority=300, proto='arp', table=50),
             mock.call(actions=(
                 'load:0xa1b2c3d4e5f6->NXM_NX_ARP_SHA[],'
                 'mod_dl_src:a1:b2:c3:d4:e5:f6,'
                 'mod_dl_dst:FF:FF:FF:FF:FF:FF,'
                 'load:0xac1804af->NXM_OF_ARP_SPA[],'
                 'load:0xac180401->NXM_OF_ARP_TPA[],'
                 'load:0x0->NXM_NX_ARP_THA[],load:0x01->NXM_OF_ARP_OP[],'
                 'load:0x0->NXM_OF_IN_PORT[],output:4,'),
                 arp_op=2, dl_dst='fa:16:3e:22:27:96', arp_tpa='***********',
                 priority=150, proto='arp', table=70),
             mock.call(actions=(
                 'load:0xa1b2c3d4e5f6->NXM_NX_ARP_SHA[],'
                 'mod_dl_src:a1:b2:c3:d4:e5:f6,'
                 'mod_dl_dst:FF:FF:FF:FF:FF:FF,'
                 'load:0xac1804af->NXM_OF_ARP_SPA[],'
                 'load:0xac180401->NXM_OF_ARP_TPA[],'
                 'load:0x0->NXM_NX_ARP_THA[],'
                 'load:0x01->NXM_OF_ARP_OP[],'
                 'load:0x0->NXM_OF_IN_PORT[],output:4,'),
                 arp_op=2, dl_src='a1:b2:c3:d4:e5:f6',
                 priority=150, proto='arp', arp_spa='************',
                 table=70)])

    def test_remove_floating_ip(self):
        ri = self._create_router()
        ri.inf_ip_of_ports = {}
        ri.inf_ip_of_ports['************'] = 3
        ri.fip_map = {'***********': '*********0'}
        with mock.patch.object(comm_sock, "notify_ovs_agent") as notify_agent:
            ri.remove_floating_ip('***********')
        ri.br.remove_fip_flows.assert_called_once_with(
            '***********', '*********0')
        notify_agent.assert_called_once_with(
            "", "", "", {"floating_ip_address": '***********'}, "DOWN")

    def test_br_remove_fip_flows(self):
        self.br.remove_fip_flows('***********', '*********0')
        self.assertEqual(self.br.delete_flows.call_args_list,
                         [mock.call(nw_src='*********0', proto='ip'),
                          mock.call(nw_dst='***********', proto='ip'),
                          mock.call(nw_src='***********', proto='ip'),
                          mock.call(arp_tpa='***********', proto='arp'),
                          mock.call(proto='arp', arp_tpa='*********0',
                                    table=70),
                          mock.call(proto='arp', arp_spa='***********',
                                    table=70),
                          mock.call(proto='ip', nw_dst='*********0',
                                    table=45)])

    def test_install_ip_forwarder_flows(self):
        src_mac = 'aa.bb.cc.dd.ee.11'
        dst_mac = 'aa.bb.cc.dd.ee.22'
        port = 1
        ip = '*********0'
        self.br.install_ip_forwarder_flows(ip, src_mac, dst_mac, port)
        self.assertEqual(self.br.add_flow.call_args_list, [
            mock.call(
                actions='mod_dl_src:aa.bb.cc.dd.ee.11,' +
                        'mod_dl_dst:aa.bb.cc.dd.ee.22,' +
                        'dec_ttl,resubmit(,60)',
                nw_dst='*********0', priority=200, proto='ip', table=50),
            mock.call(actions='strip_vlan,output:1',
                      vlan_tci='0x1000/0x1000',
                      eth_dst='aa.bb.cc.dd.ee.22',
                      eth_type='0x0800',
                      priority=101, table=60),
            mock.call(
                actions='output:1', eth_dst='aa.bb.cc.dd.ee.22',
                eth_type='0x0800', priority=100, table=60)])

    def test__set_subnet_arp_info(self):
        ri = self._create_router()
        ri.inf_ip_of_ports = {}
        ri.inf_ip_of_ports['*********'] = 1
        ri.inf_ip_of_ports['**********'] = 2
        ri.inf_mac_addresses = {}
        ri.inf_mac_addresses['**********'] = 'aa:bb:cc:dd:ee:11'
        ri.inf_mac_addresses['*********'] = 'aa:bb:cc:dd:ee:22'
        subnet = 'sub-2'
        subnet_ports = [
            {'device_owner': 'network:floatingip',
             'fixed_ips': [{'ip_address': '*********',
                            'subnet_id': 'sub-2'}],
             'mac_address': 'fa:16:3e:6d:08:50'}]

        with mock.patch.object(ri.agent, 'get_ports_by_subnet',
                               return_value=subnet_ports):
            with mock.patch.object(ri.br, 'install_ip_forwarder_flows'):
                ri._set_subnet_arp_info(subnet, 1)
                ri.br.install_ip_forwarder_flows.assert_called_once_with(
                    '*********', 'aa:bb:cc:dd:ee:22', 'fa:16:3e:6d:08:50', 1)

    def test_br_install_learn_action_flows(self):
        self.br.install_learn_action_flows(1)
        self.assertEqual(
            self.br.add_flow.call_args_list, [mock.call(
                actions='load:0x1->NXM_NX_REG4[],learn(priority=100,' +
                        'table=80,eth_type=0x0806,' +
                        'NXM_OF_ARP_TPA[]=NXM_OF_ARP_SPA[],' +
                        'output:NXM_NX_REG4[]),learn(priority=99,' +
                        'table=80,eth_type=0x0800,' +
                        'load:NXM_NX_ARP_SHA[]->NXM_OF_ETH_DST[],' +
                        'output:NXM_NX_REG4[])',
                arp_op=2, in_port=1, priority=60, proto='arp',
                reg4='0x0', table=0),
                mock.call(actions='dec_ttl,resubmit(,80)', priority=25,
                          proto='ip', table=0),
                mock.call(actions='dec_ttl,resubmit(,80)', priority=25,
                          proto='ipv6', table=0),
                mock.call(actions='DROP', priority=25, table=80),
                mock.call(actions='resubmit(,21)', priority=1, table=5)])

    def test_install_router_interface_flows(self):
        self.br.install_router_interface_flows(
            '**********', '**********/24', 7, 'aa:bb:cc:dd:ee:ff')
        self.assertEqual(self.br.add_flow.call_args_list, [
            mock.call(actions='resubmit(,21)', nw_dst='**********/24',
                      priority=100, proto='ip', table=0),
            mock.call(actions='resubmit(,21)', nw_dst='**********',
                      priority=50, proto='arp', table=0),
            mock.call(
                actions='move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],' +
                        'mod_dl_src:aa:bb:cc:dd:ee:ff,' +
                        'load:0x2->NXM_OF_ARP_OP[],' +
                        'move:NXM_NX_ARP_SHA[]->NXM_NX_ARP_THA[],' +
                        'move:NXM_OF_ARP_SPA[]->NXM_OF_ARP_TPA[],' +
                        'set_field:**********->arp_spa,' +
                        'set_field:aa:bb:cc:dd:ee:ff->arp_sha,' +
                        'resubmit(,60),resubmit(,70)',
                arp_op=1, in_port=7, nw_dst='**********',
                priority=300, proto='arp', table=50),
            mock.call(
                actions='move:NXM_OF_IP_SRC[]->NXM_OF_IP_DST[],' +
                        'mod_nw_src:**********,' +
                        'load:0x00->NXM_OF_ICMP_TYPE[],' +
                        'move:NXM_OF_ETH_SRC[]->NXM_OF_ETH_DST[],' +
                        'mod_dl_src:aa:bb:cc:dd:ee:ff,resubmit(,60)',
                icmp_type=8, nw_dst='**********', priority=300,
                proto='icmp', table=50),
            mock.call(actions='IN_PORT', eth_src='aa:bb:cc:dd:ee:ff',
                      nw_src='**********', priority=150,
                      proto='icmp', table=60)])

    def test__process_external_gateway(self):
        ri = self._create_router()
        mock.patch.object(ri, 'external_gateway_added').start()
        mock.patch.object(ri, 'external_gateway_updated').start()
        mock.patch.object(ri, 'external_gateway_removed').start()
        ex_gw_port =\
            {'fixed_ips':
                [{'ip_address': '************',
                  'prefixlen': 24,
                  'subnet_id': 'mysubnet1'}],
             'id': 'my_ex_gw_port',
             'mac_address': 'aa:bb:cc:dd:ee:ff',
             'subnets': [{'cidr': '**********/24',
                          'dns_nameservers': [],
                          'gateway_ip': '**********',
                          'id': 'mysubnet1',
                          'ipv6_ra_mode': None}]}
        ri._process_external_gateway(ex_gw_port)
        self.assertEqual(1, ri.external_gateway_added.call_count)
        ri.ex_gw_port = ex_gw_port
        ri._process_external_gateway(None)
        self.assertEqual(1, ri.external_gateway_removed.call_count)

    def test_process_external(self):
        ri = self._create_router()
        mock.patch.object(ri, '_process_external_gateway').start()
        mock.patch.object(ri, 'process_floating_ip_addresses').start()
        mock.patch.object(ri, 'get_fip_agent_gateway').start()
        ri.process_external()
        self.assertEqual(1, ri._process_external_gateway.call_count)
        ri.process_floating_ip_addresses.assert_called_once_with(
            'qg-my_ex_gw_po')

    def test_get_router_cidrs(self):
        ri = self._create_router()
        result = ri.get_router_cidrs()
        self.assertEqual(result, {'**********/24'})

    def test__get_updated_ports(self):
        ri = self._create_router()

        existing_ports = [
            {'network_id': 'my_net',
             'device_owner': 'network:router_interface_distributed',
             'fixed_ips': [{'ip_address': '********',
                            'subnet_id': 'my_sub1'}],
             'id': 'port_foo',
             'mac_address': 'fa:16:3e:35:89:42',
             'subnets': [{'cidr': '10.0.0.0/26',
                          'id': 'my-sub1'}]}]

        current_ports = [
            {'fixed_ips':
                [{'ip_address': '************',
                  'prefixlen': 24,
                  'subnet_id': 'mysubnet1'}],
             'id': 'port_foo',
             'mac_address': 'aa:bb:cc:dd:ee:ff',
             'subnets': [{'cidr': '**********/24',
                          'gateway_ip': '**********',
                          'id': 'mysubnet1'}]}]
        result = ri._get_updated_ports(existing_ports, current_ports)
        expected = {
            'port_foo': {
                'id': 'port_foo',
                'subnets': [
                    {'id': 'mysubnet1',
                     'cidr': '**********/24',
                     'gateway_ip': '**********'}],
                'fixed_ips': [{'prefixlen': 24,
                               'subnet_id': 'mysubnet1',
                               'ip_address': '************'}],
                'mac_address': 'aa:bb:cc:dd:ee:ff'}}
        self.assertEqual(result, expected)

    def test__process_internal_ports(self):
        ri = self._create_router()
        mock.patch.object(ri, '_get_updated_ports').start()
        mock.patch.object(ri, 'internal_network_added').start()
        mock.patch.object(ri, 'internal_network_removed').start()
        ri._process_internal_ports()
        ri.internal_network_added.assert_called_once_with(
            {'network_id': 'net-2', 'id': 'intf-2', 'admin_state_up': True,
             'device_owner': 'network:router_interface_distributed',
             'mac_address': 'aa:bb:cc:dd:ee:22',
             'fixed_ips': [{'prefixlen': 24, 'subnet_id': 'sub-2',
                            'ip_address': '*********'}],
             'subnets': [{'cidr': '*********/24', 'gateway_ip': '*********',
                          'id': 'sub-2', 'ipv6_ra_mode': None}]})

    def test__process_external_on_delete(self):
        ri = self._create_router()
        mock.patch.object(ri, '_process_external_gateway').start()
        mock.patch.object(ri, 'disable_floating_ip_addresses').start()
        ri._process_external_on_delete()
        ri._process_external_gateway.assert_called_once_with(
            {'id': 'my_ex_gw_port',
             'fixed_ips': [{'ip_address': '************',
                            'subnet_id': 'my-ext-sub1',
                            'prefixlen': 24}],
             'subnets': [{'id': mock.ANY,
                          'gateway_ip': None, 'cidr': '**********/24'}],
             'mac_address': 'aa:bb:cc:dd:ee:ff',
             'network_id': 'ext-net-id'})
        ri.disable_floating_ip_addresses.assert_called_once_with()

    def test_get_gw_ns_name(self):
        br_router = self._create_router()
        br_name = "dvr-myrouter"
        self.assertEqual(br_router.get_gw_ns_name(), br_name)

    def test_process_delete(self):
        ri = self._create_router()
        mock.patch.object(ri, '_process_internal_on_delete').start()
        mock.patch.object(ri, '_process_external_on_delete').start()
        ri.process_delete()
        ri.br.bridge_exists.assert_called_once_with(ri.br.br_name)
        ri._process_internal_on_delete.assert_called_once_with()
        ri._process_external_on_delete.assert_called_once_with()

    def test_test_check_fip_ns(self):
        br_router = self._create_router()
        ext_net_id = '3a220dc3-49af-47c2-8fa3-d2397f8568e4'
        with mock.patch('neutron.agent.l3.namespaces.build_ns_name') as \
                build_ns_name:
            with mock.patch('neutron.agent.linux.ip_lib.IPWrapper') as \
                    ip_wrapper:
                br_router.check_fip_ns(ext_net_id)

                self.assertEqual(build_ns_name.call_args_list, [
                    mock.call(FIP_NS_PREFIX, ext_net_id)
                ])
                # ns_name is Mock Object
                mock_ns_name = build_ns_name(FIP_NS_PREFIX, ext_net_id)
                self.assertEqual(ip_wrapper.call_args_list, [
                    mock.call(namespace=mock_ns_name)
                ])
                # ip_wrapper is Mock Object
                mock_ip_wrapper = ip_wrapper(namespace=mock_ns_name)
                self.assertEqual(mock_ip_wrapper.netns.exists.call_args_list,
                                 [mock.call(mock_ns_name)])
                mock_ip_wrapper.get_devices.assert_called_once_with()
                mock_ip_wrapper.netns.delete.assert_called_once_with(
                    mock_ns_name)

    def test_get_floating_agent_gw_interface(self):
        ri = self._create_router()
        ext_net = 'ext-net-id'
        actual_fip_port = ri.get_floating_agent_gw_interface(ext_net)
        fip_ports = ri.router.get('_floatingip_agent_interfaces', [])
        reference_fip_ports = [p for p in fip_ports
                               if p['network_id'] == ext_net]
        self.assertEqual([actual_fip_port], reference_fip_ports)

    def test_get_fip_agent_gateway(self):
        ri = self._create_router()
        ext_net_id = '3a220dc3-49af-47c2-8fa3-d2397f8568e4'
        ext_port = {'network_id': ext_net_id}

        mock_fip_agent_port = mock.MagicMock(id='0123')
        ri.agent.plugin_rpc.get_agent_gateway_port = mock.MagicMock(
            return_value=[mock_fip_agent_port])
        ri.get_floating_agent_gw_interface = mock.MagicMock(
            return_value=[mock_fip_agent_port])
        actual_fip_agent_ports = ri.get_fip_agent_gateway(ext_port)

        self.assertEqual(ri.get_floating_agent_gw_interface.call_args_list, [
            mock.call(ext_port['network_id'])
        ])
        self.assertEqual(mock_fip_agent_port, actual_fip_agent_ports[0])

    def test_process_external_added(self):
        ri = self._create_router()
        ex_gw_port = {'network_id': 'ex-net-id'}
        mock.patch.object(ri, 'get_ex_gw_port',
                          return_value=ex_gw_port).start()
        mock.patch.object(ri, '_process_external_gateway').start()
        mock.patch.object(ri, 'process_floating_ip_addresses').start()
        mock.patch.object(ri, 'check_fip_ns').start()

        mock_fig_agent_port = mock.MagicMock(id=0x12)
        mock.patch.object(ri, 'get_fip_agent_gateway',
                          return_value=mock_fig_agent_port).start()
        mock.patch.object(ri, 'ensure_fg_gateway_port_binding').start()
        mock.patch.object(ri, 'process_snat_dnat_for_fip').start()
        mock_interface = mock.MagicMock(id=0x13)
        mock.patch.object(ri, 'get_external_device_interface_name',
                          return_value=mock_interface).start()
        mock_fip_status = {"state": "active"}
        mock.patch.object(ri, 'configure_fip_addresses',
                          return_value=mock_fip_status).start()
        mock.patch.object(ri, 'update_fip_statuses').start()
        ri.process_external()

        ri.get_ex_gw_port.assert_called_once_with()
        self.assertEqual(1, ri._process_external_gateway.call_count)
        self.assertEqual(ri.check_fip_ns.call_args_list, [
            mock.call(ex_gw_port['network_id'])
        ])
        self.assertEqual(ri.get_fip_agent_gateway.call_args_list, [
            mock.call(ex_gw_port)
        ])
        self.assertEqual(ri.ensure_fg_gateway_port_binding.call_args_list,
                         [mock.call(mock_fig_agent_port)])
        ri.process_snat_dnat_for_fip.assert_called_once_with()
        ri.get_external_device_interface_name.assert_called_once_with(
            ex_gw_port)
        ri.configure_fip_addresses.assert_called_once_with(mock_interface)
        ri.update_fip_statuses.assert_called_once_with(mock_fip_status)

    def test_process_external_gateway(self):
        ri = self._create_router()
        ex_gw_port = {
            'fixed_ips': [{'ip_address': '************',
                           'prefixlen': 24,
                           'subnet_id': 'my-ext-sub1'}],
            'id': 'my_ex_gw_port',
            'mac_address': 'aa:bb:cc:dd:ee:ff',
            'subnets': [{'id': _uuid(),
                         'cidr': '**********/24',
                         'gateway_ip': None}],
            'network_id': 'ext-net-id'}

        mock.patch.object(ri, 'get_external_device_name',
                          return_value='qg-my_ex_gw_port').start()
        ri.ex_gw_port = mock.MagicMock(id=0x12)
        mock.patch.object(ri, '_gateway_ports_equal',
                          return_value=False).start()
        mock.patch.object(ri, 'external_gateway_updated').start()
        ri._process_external_gateway(ex_gw_port)

        self.assertEqual(ri.get_external_device_name.call_args_list, [
            mock.call('my_ex_gw_port')
        ])
        ri._gateway_ports_equal.assert_called_once_with(ex_gw_port,
                                                        ri.ex_gw_port)
        self.assertEqual(ri.external_gateway_updated.call_args_list, [
            mock.call(ex_gw_port, 'qg-my_ex_gw_port')
        ])

    def test_filter_defer_apply_on(self):
        ri = self._create_router()
        ri.filter_defer_apply_on()
        ri.br.enable_defer.assert_called_once_with()

    def test_filter_defer_apply_off(self):
        ri = self._create_router()
        ri.filter_defer_apply_off()
        ri.br.defer_apply_flows()
        ri.br.disable_defer()

    def test_defer_apply(self):
        ri = self._create_router()
        mock.patch.object(ri, 'filter_defer_apply_on').start()
        mock.patch.object(ri, 'filter_defer_apply_off').start()
        with ri.defer_apply():
            pass
        ri.filter_defer_apply_on.assert_called_once_with()
        ri.filter_defer_apply_off.assert_called_once_with()

    def test_process_port_allowed_pairs(self):
        ri = self._create_router()
        internal_ports = [
            {'device_owner': 'network:router_interface_distributed',
             'network_id': 'net-2',
             'fixed_ips': [
                 {'ip_address': '*********', 'subnet_id': 'sub-2',
                  'prefixlen': 24}],
             'id': 'intf-2',
             'admin_state_up': True,
             'subnets': [
                 {'id': 'sub-2', 'ipv6_ra_mode': None,
                  'gateway_ip': '*********', 'cidr': '*********/24'}
             ], 'mac_address': 'aa:bb:cc:dd:ee:22'}
        ]
        with mock.patch.object(ri, '_update_arp_entry'):
            ri.process_port_allowed_pairs()
            self.assertEqual(ri.router.get('_interfaces'), internal_ports)
            self.assertEqual(ri._update_arp_entry.call_args_list, [
                mock.call('************',
                          'aa:cc:dd:ff:bb:ee',
                          '3a220dc3-49af-47c2-8fa3-d2397f8568e4',
                          'add')
            ])

    def test_process(self):
        ri = self._create_router()
        mock.patch.object(ri, 'defer_apply').start()
        mock.patch.object(ri, '_process_internal_ports').start()
        mock.patch.object(ri, 'process_external').start()
        mock.patch.object(ri, 'process_address_scope').start()
        mock.patch.object(ri, 'process_port_allowed_pairs').start()
        mock.patch.object(ri, 'get_ex_gw_port').start()
        ri.process()
        ri.defer_apply.assert_called_once_with()
        self.assertEqual(ri.defer_apply.call_args_list, [mock.call()])
        self.assertEqual(ri._process_internal_ports.call_args_list,
                         [mock.call()])
        self.assertEqual(ri.process_external.call_args_list, [mock.call()])
        self.assertEqual(ri.process_address_scope.call_args_list, [
            mock.call()])
        self.assertEqual(ri.process_port_allowed_pairs.call_args_list, [
            mock.call()])
        self.assertEqual(ri.get_ex_gw_port.call_args_list, [mock.call()])

    def test_process_internal_on_delete(self):
        ri = self._create_router()
        ri.internal_ports = ri.router['_interfaces']
        ri._process_internal_on_delete()
        ri.br.delete_port.assert_called_once_with('intpintf-2')
        ri.int_br.delete_port.assert_called_once_with('qr-intf-2')


class TestNativeDvrLocalBridgeRouter(base.BaseTestCase):
    def setUp(self):
        super(TestNativeDvrLocalBridgeRouter, self).setUp()
        conn_patcher = mock.patch('neutron.agent.ovsdb.impl_idl._connection')
        conn_patcher.start()
        self.conf = agent_config.setup_conf()
        self.conf.register_opts(agent_config.AGENT_STATE_OPTS, 'AGENT')
        l3_config.register_l3_agent_config_opts(l3_config.OPTS, self.conf)
        l3_config.register_l3_agent_ovs_opts(l3_config.ovs_opts, self.conf)
        agent_config.register_interface_opts(self.conf)
        ovs_opt = cfg.StrOpt('foo')
        self.conf.register_opt(ovs_opt, group='OVS')
        self.router_id = 'myrouter'
        self.port = {
            'network_id': _uuid(),
            'device_owner': 'network:router_interface_distributed',
            'fixed_ips': [{
                'ip_address': '********',
                'subnet_id': 'my-sub2'
            }],
            'id': 'port_foo',
            'mac_address': 'fa:16:3e:35:89:42',
            'subnets': [{
                'cidr': '10.0.0.0/26',
                'id': 'my-sub2'
            }]
        }
        bridge_class = functools.partial(native_dvr_bridge.L3AgentBridge,
                                         ryu_app=self)
        self.setup_bridge_mock("test", bridge_class)

    def setup_bridge_mock(self, name, cls):
        self.br = cls(name)
        mock_uninstall_flows = mock.patch.object(self.br,
                                                 'uninstall_flows').start()
        mock_install_normal = mock.patch.object(self.br,
                                                'install_normal').start()
        mock_install_drop = mock.patch.object(self.br, 'install_drop').start()
        mock_install_goto = mock.patch.object(self.br, 'install_goto').start()
        mock_install_instructions = mock.patch.object(
            self.br, 'install_instructions').start()

        self.mock = mock.Mock()
        self.mock.attach_mock(mock_uninstall_flows, 'uninstall_flows')
        self.mock.attach_mock(mock_install_normal, 'install_normal')
        self.mock.attach_mock(mock_install_drop, 'install_drop')
        self.mock.attach_mock(mock_install_goto, 'install_goto')
        self.mock.attach_mock(mock_install_instructions,
                              'install_instructions')
        self.dp = mock.Mock()
        self.ofp = mock.Mock()
        self.ofpp = mock.Mock()
        self.default_cookie = '0x1234'
        self.br._get_dp = mock.Mock(
            return_value=[self.dp, self.ofp, self.ofpp])

    def test_br_init_bridge_flows(self):
        self.br.init_bridge_flows()
        self.assertEqual(self.br.uninstall_flows.call_args_list,
                         [mock.call(cookie=0)])
        self.assertEqual(self.br.install_normal.call_args_list, [
            mock.call(table_id=l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE,
                      priority=1),
        ])
        self.assertEqual(self.br.install_drop.call_args_list, [
            mock.call(table_id=l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE,
                      priority=1)
        ])
        self.assertEqual(self.br.install_goto.call_args_list, [
            mock.call(dest_table_id=l3_constants.DVR_BRIDGE_FIP_AD_PORTS,
                      table_id=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
                      priority=1),
            mock.call(dest_table_id=l3_constants.DVR_BRIDGE_FIP_QOS,
                      table_id=l3_constants.DVR_BRIDGE_FIP_AD_PORTS,
                      priority=1),
            mock.call(dest_table_id=l3_constants.DVR_BRIDGE_FIP_NAT,
                      table_id=l3_constants.DVR_BRIDGE_FIP_QOS,
                      priority=1),
            mock.call(dest_table_id=l3_constants.DVR_BRIDGE_ACL,
                      table_id=l3_constants.DVR_BRIDGE_FIP_NAT,
                      priority=1),
            mock.call(dest_table_id=l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE,
                      table_id=l3_constants.DVR_BRIDGE_ACL,
                      priority=1),
            mock.call(dest_table_id=l3_constants.DVR_BRIDGE_LOCAL_SNAT_TABLE,
                      table_id=l3_constants.DVR_BRIDGE_PRE_SNAT_ACL,
                      priority=1)
        ])

    @mock.patch('neutron.agent.common.ovs_lib.OVSBridge')
    def _create_router(self, router=None, **kwargs):
        router = {
            '_floatingips': [{
                'id': 'my_fip1',
                'host': 'some-other-host',
                'floating_ip_address': '***********',
                'fixed_ip_address': '*********0',
                'floating_network_id': 'my_fnw1',
                'port_id': 'my_port1'
            }],
            '_interfaces': [{
                'fixed_ips': [{
                    'ip_address': '*********',
                    'prefixlen': 24,
                    'subnet_id': 'sub-2'
                }],
                'device_owner':
                'network:router_interface_distributed',
                'admin_state_up':
                True,
                'id':
                'intf-2',
                'mac_address':
                'aa:bb:cc:dd:ee:22',
                'network_id':
                'net-2',
                'subnets': [{
                    'cidr': '*********/24',
                    'gateway_ip': '*********',
                    'id': 'sub-2',
                    'ipv6_ra_mode': None
                }]
            }],
            'id':
            '3a220dc3-49af-47c2-8fa3-d2397f8568e4',
            'gw_port': {
                'fixed_ips': [{
                    'ip_address': '************',
                    'prefixlen': 24,
                    'subnet_id': 'my-ext-sub1'
                }],
                'id':
                'my_ex_gw_port',
                'mac_address':
                'aa:bb:cc:dd:ee:ff',
                'subnets': [{
                    'id': _uuid(),
                    'cidr': '**********/24',
                    'gateway_ip': None
                }],
                'network_id':
                'ext-net-id'
            },
            'routes': []
        }

        kwargs['router_id'] = self.router_id
        kwargs['router'] = router
        kwargs['use_ipv6'] = False
        kwargs['agent_conf'] = self.conf
        agent = mock.MagicMock()
        bridge_router = dvr_bridge.DvrLocalBridgeRouter(
            HOSTNAME, agent, **kwargs)
        bridge_router.br = mock.MagicMock()
        bridge_router.int_br = mock.MagicMock()
        bridge_router.ex_br = mock.MagicMock()
        return bridge_router

    def test_internal_network_added(self):
        ri = self._create_router()
        mock.patch.object(ri.br, 'install_router_interface_flows').start()
        mock.patch.object(ri, '_set_subnet_arp_info').start()
        attrs = [('type', 'patch'), ('options', {
            'peer': 'intpport_foo'
        }),
                 ('external_ids', {
                     'iface-status': 'active',
                     'attached-mac': 'fa:16:3e:35:89:42',
                     'iface-id': 'port_foo'
                 })]
        ri.internal_network_added(self.port)

        # generate_router_interface_flows should be called the same number of
        # as fixed_ips in ports
        self.assertEqual(1, ri.br.install_router_interface_flows.call_count)

        # make sure patch port was added once on the integration bridge
        ri.int_br.add_port.assert_called_once_with('qr-port_foo', *attrs)

        # make sure patch port was added once on dvr bridge
        ri.br.add_patch_port.assert_called_once_with('intpport_foo',
                                                     'qr-port_foo')

        # make sure _set_subnet_arp_info called the same number of times as
        # there are subnets in port["subnets"]
        self.assertEqual(1, ri._set_subnet_arp_info.call_count)

    def test_internal_network_removed(self):
        ri = self._create_router()
        mock.patch.object(ri, '_set_subnet_arp_info').start()
        ri.internal_network_removed(self.port)

        ri.br.delete_port.assert_called_once_with('intpport_foo')
        ri.int_br.delete_port.assert_called_once_with('qr-port_foo')
        self.assertEqual(1, ri.br.remove_router_interface_ip_flows.call_count)

    def test_subnet_update(self):
        ri = self._create_router()
        ri.inf_ip_of_ports = {'*********': 1, '**********': 2}
        ri.inf_mac_addresses = {'**********': 'aa:bb:cc:dd:ee:11',
                                '*********': 'aa:bb:cc:dd:ee:22'}
        ri.agent.router_info = {'myrouter1': ri}
        subnet = {
            'cidr': '*********/24',
            'gateway_ip': '*********',
            'id': 'sub-2',
            'ipv6_ra_mode': None
        }
        router_ips = ['*********']
        mac_address = 'fa:16:3e:fc:ee:91'
        next_hop_table = l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE
        int_output_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE
        ri.subnet_update(subnet, router_ips, mac_address, source_router=ri)
        self.assertEqual(ri.br.install_route_goto.call_count, 3)
        ri.br.install_route_goto.assert_has_calls([
            mock.call(next_hop_table, int_output_table, 100,
                      'aa:bb:cc:dd:ee:22', 'fa:16:3e:fc:ee:91', '*********',
                      4),
            mock.call(next_hop_table, int_output_table, 100,
                      'aa:bb:cc:dd:ee:22', 'aa:bb:cc:dd:ee:22', '*********',
                      4),
            mock.call(next_hop_table, int_output_table, 100,
                      'aa:bb:cc:dd:ee:22', 'aa:bb:cc:dd:ee:11', '**********',
                      4)
        ],
                                                  any_order=True)

    def test_update_routing_table(self):
        ri = self._create_router()
        ri.inf_ip_of_ports = {'*********': 1, '**********': 2}
        ri.inf_mac_addresses = {'**********': 'aa:bb:cc:dd:ee:11',
                                '*********': 'aa:bb:cc:dd:ee:22'}
        ri.agent.router_info = {'myrouter1': ri}
        route1 = {'destination': '*********/24', 'nexthop': '**********'}
        test_ports = [{
            'mac_address':
            'aa:bb:cc:dd:ee:11',
            'fixed_ips': [{
                'ip_address': '**********',
                'prefixlen': 24,
                'subnet_id': 'mysubnet1'
            }]
        }]

        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        ouput_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE
        with mock.patch.object(ri.agent,
                               'get_ports_by_subnet',
                               return_value=test_ports):
            ri.update_routing_table('replace', route1)
        self.assertEqual(ri.br.install_route_goto.call_count, 1)
        self.assertEqual(ri.br.install_route_goto.call_args_list, [
            mock.call(input_table, ouput_table, 250, 'aa:bb:cc:dd:ee:22',
                      'aa:bb:cc:dd:ee:11', '*********/24', 4)
        ])

        with mock.patch.object(ri.agent,
                               'get_ports_by_subnet',
                               return_value=test_ports):
            ri.update_routing_table('delete', route1)
        self.assertEqual(ri.br.remove_route_goto.call_args_list,
                         [mock.call(input_table, '*********/24', 4)])

    def test_routes_updated(self):
        ri = self._create_router()
        old_routes = [{'destination': '*********/24', 'nexthop': '**********'}]
        new_routes = [{'destination': '*********/24', 'nexthop': '*********'}]

        with mock.patch.object(ri, 'update_routing_table'):
            ri.routes_updated(old_routes, new_routes)
            self.assertEqual(ri.update_routing_table.call_args_list, [
                mock.call('replace', {
                    'nexthop': '*********',
                    'destination': '*********/24'
                }),
                mock.call('delete', {
                    'nexthop': '**********',
                    'destination': '*********/24'
                })
            ])

    def test_external_gateway_added(self):
        ex_gw_port = {
            'fixed_ips': [{
                'ip_address': '************',
                'prefixlen': 24,
                'subnet_id': 'mysubnet1'
            }],
            'id':
            'my_ex_gw_port',
            'mac_address':
            'aa:bb:cc:dd:ee:ff',
            'subnets': [{
                'cidr': '**********/24',
                'dns_nameservers': [],
                'gateway_ip': '**********',
                'id': 'mysubnet1',
                'ipv6_ra_mode': None,
                'subnetpool_id': None
            }]
        }

        interface_name = 'qg-7c9e26d9-1e'
        ri = self._create_router()
        mock.patch.object(ri.br, 'install_learn_action_flows').start()
        mock.patch.object(ri.br, 'add_flow').start()
        with mock.patch.object(ri.br, 'get_port_ofport', return_value=3):
            ri.external_gateway_added(ex_gw_port, interface_name)
        attrs = [('type', 'patch'), ('options', {
            'peer': 'ex-7c9e26d9-1e'
        }),
                 ('external_ids', {
                     'attached-mac': 'aa:bb:cc:dd:ee:ff',
                     'iface-id': 'my_ex_gw_port',
                     'iface-status': 'active'
                 })]
        self.assertEqual(ri.br.add_patch_port.call_args_list,
                         [mock.call('ex-7c9e26d9-1e', 'qg-7c9e26d9-1e')])
        self.assertEqual(ri.int_br.add_port.call_args_list,
                         [mock.call('qg-7c9e26d9-1e', *attrs)])
        self.assertEqual(ri.br.install_learn_action_flows.call_args_list,
                         [mock.call(3)])

    def test_external_gateway_removed(self):
        ex_gw_port = {
            'fixed_ips': [{
                'ip_address': '************',
                'prefixlen': 24,
                'subnet_id': 'mysubnet1'
            }],
            'id':
            'my_ex_gw_port',
            'mac_address':
            'aa:bb:cc:dd:ee:ff',
            'subnets': [{
                'cidr': '**********/24',
                'dns_nameservers': [],
                'gateway_ip': '**********',
                'id': 'mysubnet1',
                'ipv6_ra_mode': None,
                'subnetpool_id': None
            }]
        }
        ri = self._create_router()
        with mock.patch.object(ri.br, 'get_port_ofport', return_value=3):
            interface_name = 'qg-7c9e26d9-1e'
            ri.external_gateway_removed(ex_gw_port, interface_name)
        self.assertEqual(ri.br.delete_port.call_args_list,
                         [mock.call('ex-7c9e26d9-1e')])
        self.assertEqual(ri.int_br.delete_port.call_args_list,
                         [mock.call('qg-7c9e26d9-1e')])
        self.assertEqual(ri.br.remove_external_gateway_flows.call_args_list,
                         [mock.call(ex_gw_port)])

    def test_br_remove_external_gateway_flows(self):
        ex_gw_port = {
            'fixed_ips': [{
                'ip_address': '************',
                'prefixlen': 24,
                'subnet_id': 'mysubnet1'
            }],
            'id':
            'my_ex_gw_port',
            'mac_address':
            'aa:bb:cc:dd:ee:ff',
            'subnets': [{
                'cidr': '**********/24',
                'dns_nameservers': [],
                'gateway_ip': '**********',
                'id': 'mysubnet1',
                'ipv6_ra_mode': None,
                'subnetpool_id': None
            }]
        }
        self.br.remove_external_gateway_flows(ex_gw_port)
        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        mac_learning_table = l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE
        ext_output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        gateway_mac = ex_gw_port['mac_address']
        ip = ex_gw_port['fixed_ips'][0]['ip_address']
        self.assertEqual(self.br.uninstall_flows.call_args_list, [
            mock.call(table_id=input_table,
                      eth_type=ether_types.ETH_TYPE_ARP,
                      reg4=0),
            mock.call(table_id=mac_learning_table,
                      eth_type=ether_types.ETH_TYPE_ARP),
            mock.call(table_id=mac_learning_table,
                      eth_type=ether_types.ETH_TYPE_IPV6,
                      ip_proto=in_proto.IPPROTO_ICMPV6),
            mock.call(eth_type=ether_types.ETH_TYPE_IP, eth_src=gateway_mac),
            mock.call(eth_type=ether_types.ETH_TYPE_IP,
                      ip_proto=in_proto.IPPROTO_ICMP,
                      eth_src=gateway_mac),
            mock.call(eth_type=ether_types.ETH_TYPE_ARP, arp_tpa=ip),
            mock.call(eth_type=ether_types.ETH_TYPE_IP, ipv4_dst=ip),
            mock.call(table_id=ext_output_table)
        ])

    def test_process_floating_ip_addresses(self):
        ri = self._create_router()
        ri.inf_ip_of_ports = {'************': 3}
        interface_name = 'qg-d9a6f930-dc'
        with mock.patch.object(ri, 'add_floating_ip'):
            with mock.patch.object(ri, 'remove_floating_ip'):
                ri.process_floating_ip_addresses(interface_name)
                ri.add_floating_ip.assert_called_once_with(
                    {
                        'fixed_ip_address': '*********0',
                        'port_id': 'my_port1',
                        'floating_network_id': 'my_fnw1',
                        'floating_ip_address': '***********',
                        'id': 'my_fip1',
                        'host': 'some-other-host'
                    }, 'qg-d9a6f930-dc')

    def test_add_floating_ip(self):
        fip = {
            'fixed_ip_address': '***********',
            'floating_ip_address': '************',
            'floating_network_id': 'ext_net',
            'host': 'my_host',
            'id': 'my_fip',
            'port_details': {
                'device_id': 'nova_id',
                'device_owner': 'compute:nova',
                'mac_address': 'fa:16:3e:22:27:96',
                'network_id': 'my_net',
                'status': 'ACTIVE'
            },
            'port_id': 'my_port1',
            'router_id': 'my_router',
            'admin_state_up': True,
            'denied_port_numbers': [1, 2]
        }
        ex_gw_port = {
            'fixed_ips': [{
                'ip_address': '************',
                'prefixlen': 24,
                'subnet_id': 'mysubnet1'
            }],
            'id':
            'my_ex_gw_port',
            'network_id':
            'ext_net',
            'mac_address':
            'aa:bb:cc:dd:ee:ff',
            'subnets': [{
                'cidr': '**********/24',
                'dns_nameservers': [],
                'gateway_ip': '**********',
                'id': 'mysubnet1',
                'ipv6_ra_mode': None,
                'subnetpool_id': None
            }]
        }
        fg_port = {
            'fixed_ips': [{
                'ip_address': '************',
                'prefixlen': 24,
                'subnet_id': 'mysubnet1'
            }],
            'id':
            'my_fg_port',
            'network_id':
            'ext_net',
            'mac_address':
            'a1:b2:c3:d4:e5:f6',
            'subnets': [{
                'cidr': '**********/24',
                'dns_nameservers': [],
                'gateway_ip': '**********',
                'id': 'mysubnet1',
                'ipv6_ra_mode': None,
                'subnetpool_id': None
            }]
        }
        interface_name = 'qg-d9a6f930-dc'
        ri = self._create_router()
        ri.inf_ip_of_ports = {'************': 3, '************': 4}
        mock.patch.object(ri, 'get_fip_agent_gateway',
                          return_value=fg_port).start()
        mock.patch.object(ri, 'get_ex_gw_port',
                          return_value=ex_gw_port).start()
        mock.patch.object(ri.agent.plugin_rpc,
                          'get_subnets_by_network',
                          return_value=[{
                              'cidr': '**********/24',
                              'gateway_ip': '**********'
                          }]).start()
        fip['ex_cidr'] = '**********/24'
        ri.add_floating_ip(fip, interface_name)
        self.assertEqual(ri.br.install_fip_flows.call_args_list, [
            mock.call(fip['floating_ip_address'],
                      fip['fixed_ip_address'],
                      '**********',
                      fg_port['mac_address'],
                      fip['port_details']['mac_address'],
                      4,
                      '**********/24',
                      internal_cidr='')
        ])
        self.assertEqual(ri.br.remove_drop_fip_admin_state.call_args_list,
                         [mock.call(fip['floating_ip_address'])])
        self.assertEqual(ri.br.remove_drop_fip_ad_ports.call_args_list,
                         [mock.call(fip['floating_ip_address'])])
        self.assertEqual(ri.br.install_drop_fip_ad_ports.call_args_list, [
            mock.call(fip['floating_ip_address'], 1),
            mock.call(fip['floating_ip_address'], 2)
        ])

    def test_br_install_fip_flows(self):
        fip = {
            'fixed_ip_address': '***********',
            'floating_ip_address': '************',
            'floating_network_id': 'ext_net',
            'host': 'my_host',
            'id': 'my_fip',
            'port_details': {
                'device_id': 'nova_id',
                'device_owner': 'compute:nova',
                'mac_address': 'fa:16:3e:22:27:96',
                'network_id': 'my_net',
                'status': 'ACTIVE'
            },
            'port_id': 'my_port1',
            'router_id': 'my_router',
            'admin_state_up': True,
            'denied_port_numbers': [1, 2]
        }
        float_ip = fip['floating_ip_address']
        fixed_ip = fip['fixed_ip_address']
        gate_ip = '**********'
        fg_mac = 'a1:b2:c3:d4:e5:f6'
        fixed_port_mac = fip['port_details']['mac_address']
        gateway_ip_ofport = 4
        fip_subnet_cidr = '**********/24'
        internal_cidr = '*********/24'
        self.br.install_fip_flows(float_ip,
                                  fixed_ip,
                                  gate_ip,
                                  fg_mac,
                                  fixed_port_mac,
                                  gateway_ip_ofport,
                                  fip_subnet_cidr,
                                  internal_cidr=internal_cidr)

        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        fip_nat_table = l3_constants.DVR_BRIDGE_FIP_NAT
        next_hop_table = l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE
        acl_table = l3_constants.DVR_BRIDGE_ACL
        admin_state_table = l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE
        output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        mac_learn_table = l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE
        int_out_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE
        snat_table = l3_constants.DVR_BRIDGE_LOCAL_SNAT_TABLE
        pre_snat_acl_table = l3_constants.DVR_BRIDGE_PRE_SNAT_ACL

        fip = netaddr.IPAddress(float_ip)
        ofp, ofpp = self.ofp, self.ofpp

        match_01 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                 ipv4_dst=float_ip)
        actions_01 = [
            ofpp.OFPActionSetField(ipv4_dst=fixed_ip),
        ]
        instructions_01 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_01),
            ofpp.OFPInstructionGotoTable(table_id=acl_table)
        ]
        match_02 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                 ipv4_src=fip_subnet_cidr,
                                 ipv4_dst=float_ip)
        flow_specs_02 = [
            ofpp.NXFlowSpecMatch(src=ether_types.ETH_TYPE_IP,
                                 dst=('eth_type_nxm', 0),
                                 n_bits=16),
            ofpp.NXFlowSpecMatch(src=('ipv4_src_nxm', 0),
                                 dst=('ipv4_dst_nxm', 0),
                                 n_bits=32),
            ofpp.NXFlowSpecMatch(src=('reg4', 0),
                                 dst=('ipv4_src_nxm', 0),
                                 n_bits=32),
            ofpp.NXFlowSpecLoad(src=int(fip),
                                dst=('ipv4_src_nxm', 0),
                                n_bits=32),
            ofpp.NXFlowSpecLoad(src=('eth_src_nxm', 0),
                                dst=('eth_dst_nxm', 0),
                                n_bits=48),
            ofpp.NXFlowSpecLoad(src=int(
                netaddr.EUI(fg_mac, dialect=netaddr.mac_unix)),
                                dst=('eth_src_nxm', 0),
                                n_bits=48),
            ofpp.NXFlowSpecOutput(src=('reg3', 0), dst='', n_bits=32),
        ]
        actions_02 = [
            ofpp.OFPActionSetField(reg3=gateway_ip_ofport),
            ofpp.OFPActionSetField(reg4=int(fip)),
            ofpp.NXActionLearn(
                table_id=output_table,
                cookie=self.default_cookie,
                priority=100,
                idle_timeout=l3_constants.DVR_LEARN_IDLE_TIMEOUT,
                specs=flow_specs_02)
        ]
        instructions_02 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_02),
            ofpp.OFPInstructionGotoTable(table_id=snat_table)
        ]
        match_03 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                 ipv4_src=fixed_ip)
        self.assertEqual(self.br.install_goto.call_args_list, [
            mock.call(dest_table_id=snat_table,
                      table_id=input_table,
                      priority=50,
                      eth_type=ether_types.ETH_TYPE_IP,
                      ipv4_dst=float_ip),
            mock.call(dest_table_id=admin_state_table,
                      table_id=input_table,
                      priority=50,
                      eth_type=ether_types.ETH_TYPE_ARP,
                      arp_tpa=float_ip),
            mock.call(dest_table_id=pre_snat_acl_table,
                      table_id=input_table,
                      priority=30,
                      match=match_03),
            mock.call(dest_table_id=next_hop_table,
                      table_id=acl_table,
                      priority=60010,
                      eth_type=ether_types.ETH_TYPE_IP,
                      ipv4_src=float_ip),
            mock.call(dest_table_id=output_table,
                      table_id=next_hop_table,
                      priority=80,
                      eth_type=ether_types.ETH_TYPE_IP,
                      ipv4_src=float_ip)
        ])

        match_04 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                 ipv4_src=fixed_ip)
        actions_04 = [
            ofpp.OFPActionSetField(eth_src=fg_mac),
            ofpp.OFPActionSetField(ipv4_src=float_ip),
            ofpp.OFPActionDecNwTtl()
        ]
        instructions_04 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_04),
            ofpp.OFPInstructionGotoTable(table_id=admin_state_table)
        ]

        match_05 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP,
                                 arp_op=arp.ARP_REQUEST,
                                 arp_tpa=float_ip)
        actions_05 = [
            ofpp.NXActionRegMove(src_field='eth_src',
                                 dst_field='eth_dst',
                                 n_bits=48),
            ofpp.OFPActionSetField(eth_src=fg_mac),
            ofpp.OFPActionSetField(arp_op=arp.ARP_REPLY),
            ofpp.NXActionRegMove(src_field='arp_sha',
                                 dst_field='arp_tha',
                                 n_bits=48),
            ofpp.NXActionRegMove(src_field='arp_spa',
                                 dst_field='arp_tpa',
                                 n_bits=32),
            ofpp.OFPActionSetField(arp_spa=float_ip),
            ofpp.OFPActionSetField(arp_sha=fg_mac),
            ofpp.NXActionResubmitTable(table_id=int_out_table),
            ofpp.NXActionResubmitTable(table_id=mac_learn_table)
        ]
        instructions_05 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_05)
        ]

        match_06 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP,
                                 arp_op=arp.ARP_REPLY,
                                 eth_dst=fixed_port_mac,
                                 arp_tpa=fixed_ip)
        fip_mac_arp_responder_actions = [
            ofpp.OFPActionSetField(arp_sha=fg_mac),
            ofpp.OFPActionSetField(eth_src=fg_mac),
            ofpp.OFPActionSetField(eth_dst="ff:ff:ff:ff:ff:ff"),
            ofpp.OFPActionSetField(arp_spa=float_ip),
            ofpp.OFPActionSetField(arp_tpa=gate_ip),
            ofpp.OFPActionSetField(arp_tha="00:00:00:00:00:00"),
            ofpp.OFPActionSetField(arp_op=arp.ARP_REQUEST),
            # ofpp.OFPActionSetField(in_port=0),
            ofpp.OFPActionOutput(gateway_ip_ofport, 0)
        ]
        instructions_06 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS,
                                       fip_mac_arp_responder_actions)
        ]

        match_07 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP,
                                 arp_op=arp.ARP_REPLY,
                                 eth_src=fg_mac,
                                 arp_spa=float_ip)
        instructions_07 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS,
                                       fip_mac_arp_responder_actions)
        ]
        if internal_cidr:
            match_08 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                     ipv4_src=float_ip,
                                     ipv4_dst=internal_cidr)
            actions_08 = [ofpp.OFPActionOutput(ofp.OFPP_IN_PORT, 0)]
            instructions_08 = [
                ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS,
                                           actions_08),
            ]
            self.assertEqual(self.br.install_instructions.call_args_list, [
                mock.call(table_id=fip_nat_table,
                          priority=50,
                          instructions=instructions_01,
                          match=match_01),
                mock.call(table_id=input_table,
                          priority=51,
                          instructions=instructions_02,
                          match=match_02),
                mock.call(table_id=snat_table,
                          priority=30,
                          instructions=instructions_04,
                          match=match_04),
                mock.call(table_id=next_hop_table,
                          priority=300,
                          instructions=instructions_05,
                          match=match_05),
                mock.call(table_id=mac_learn_table,
                          priority=150,
                          instructions=instructions_06,
                          match=match_06),
                mock.call(table_id=mac_learn_table,
                          priority=150,
                          instructions=instructions_07,
                          match=match_07),
                mock.call(table_id=int_out_table,
                          priority=101,
                          match=match_08,
                          instructions=instructions_08)
            ])
        else:
            self.assertEqual(self.br.install_instructions.call_args_list, [
                mock.call(table_id=fip_nat_table,
                          priority=50,
                          instructions=instructions_01,
                          match=match_01),
                mock.call(table_id=input_table,
                          priority=51,
                          instructions=instructions_02,
                          match=match_02),
                mock.call(table_id=snat_table,
                          priority=30,
                          instructions=instructions_04,
                          match=match_04),
                mock.call(table_id=next_hop_table,
                          priority=300,
                          instructions=instructions_05,
                          match=match_05),
                mock.call(table_id=mac_learn_table,
                          priority=150,
                          instructions=instructions_06,
                          match=match_06),
                mock.call(table_id=mac_learn_table,
                          priority=150,
                          instructions=instructions_07,
                          match=match_07)
            ])

    def test_remove_floating_ip(self):
        ri = self._create_router()
        ri.inf_ip_of_ports = {'************': 3}
        ri.fip_map = {'***********': '*********0'}
        with mock.patch.object(comm_sock, "notify_ovs_agent") as notify_agent:
            ri.remove_floating_ip('***********')
        ri.br.remove_fip_flows.assert_called_once_with('***********',
                                                       '*********0')
        notify_agent.assert_called_once_with(
            "", "", "", {"floating_ip_address": '***********'}, "DOWN")

    def test_br_remove_fip_flows(self):
        float_ip = '***********'
        fixed_ip = '*********0'
        self.br.remove_fip_flows(float_ip, fixed_ip)
        self.assertEqual(self.br.uninstall_flows.call_args_list, [
            mock.call(eth_type=ether_types.ETH_TYPE_IP, ipv4_src=fixed_ip),
            mock.call(eth_type=ether_types.ETH_TYPE_IP, ipv4_dst=float_ip),
            mock.call(eth_type=ether_types.ETH_TYPE_IP, ipv4_src=float_ip),
            mock.call(eth_type=ether_types.ETH_TYPE_ARP, arp_tpa=float_ip),
            mock.call(eth_type=ether_types.ETH_TYPE_ARP,
                      arp_tpa=fixed_ip,
                      table_id=l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE),
            mock.call(eth_type=ether_types.ETH_TYPE_ARP,
                      arp_spa=float_ip,
                      table_id=l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE),
            mock.call(eth_type=ether_types.ETH_TYPE_IP,
                      ipv4_dst=fixed_ip,
                      table_id=l3_constants.DVR_BRIDGE_ACL)
        ])

    def test_install_ip_forwarder_flows(self):
        src_mac = 'aa.bb.cc.dd.ee.11'
        dst_mac = 'aa.bb.cc.dd.ee.22'
        port = 1
        ip = '*********0'
        self.br.install_ip_forwarder_flows(ip, src_mac, dst_mac, port)

        ofp, ofpp = self.ofp, self.ofpp
        next_hop_table = l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE
        output_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE
        match_01 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP, ipv4_dst=ip)
        actions_01 = [
            ofpp.OFPActionSetField(eth_src=src_mac),
            ofpp.OFPActionSetField(eth_dst=dst_mac),
            ofpp.OFPActionDecNwTtl()
        ]
        instructions_01 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_01),
            ofpp.OFPInstructionGotoTable(table_id=output_table)
        ]
        match_02 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                 eth_dst=dst_mac)
        actions_02 = [ofpp.OFPActionOutput(port, 0)]
        instructions_02 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_02)
        ]
        actions_03 = [ofpp.OFPActionPopVlan(),
                      ofpp.OFPActionOutput(port, 0)]
        instructions_03 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_03)
        ]
        self.assertEqual(self.br.install_instructions.call_args_list, [
            mock.call(table_id=next_hop_table,
                      priority=200,
                      instructions=instructions_01,
                      match=match_01),
            mock.call(instructions=instructions_03,
                      match=match_02,
                      priority=101, table_id=output_table),
            mock.call(table_id=output_table,
                      priority=100,
                      instructions=instructions_02,
                      match=match_02)
        ])

    def test__set_subnet_arp_info(self):
        ri = self._create_router()
        ri.inf_ip_of_ports = {'*********': 1, '**********': 2}
        ri.inf_mac_addresses = {'**********': 'aa:bb:cc:dd:ee:11',
                                '*********': 'aa:bb:cc:dd:ee:22'}
        subnet = 'sub-2'
        subnet_ports = [{
            'device_owner':
            'network:floatingip',
            'fixed_ips': [{
                'ip_address': '*********',
                'subnet_id': 'sub-2'
            }],
            'mac_address':
            'fa:16:3e:6d:08:50'
        }]

        with mock.patch.object(ri.agent,
                               'get_ports_by_subnet',
                               return_value=subnet_ports):
            with mock.patch.object(ri.br, 'install_ip_forwarder_flows'):
                ri._set_subnet_arp_info(subnet, 1)
                ri.br.install_ip_forwarder_flows.assert_called_once_with(
                    '*********', 'aa:bb:cc:dd:ee:22', 'fa:16:3e:6d:08:50', 1)

    def test_br_install_learn_action_flows(self):
        ofport = 1
        self.br.install_learn_action_flows(ofport)
        ofp, ofpp = self.ofp, self.ofpp
        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        ext_output_table = l3_constants.DVR_BRIDGE_EXT_OUTPUT_TABLE
        snat_table = l3_constants.DVR_BRIDGE_LOCAL_SNAT_TABLE

        match_01 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP,
                                 in_port=ofport,
                                 reg4=0,
                                 arp_op=arp.ARP_REPLY)
        flow_specs_01 = [
            ofpp.NXFlowSpecMatch(src=ether_types.ETH_TYPE_ARP,
                                 dst=('eth_type_nxm', 0),
                                 n_bits=16),
            ofpp.NXFlowSpecMatch(src=('arp_spa_nxm', 0),
                                 dst=('arp_tpa_nxm', 0),
                                 n_bits=32),
            ofpp.NXFlowSpecOutput(src=('reg4', 0), dst='', n_bits=32),
        ]
        flow_specs_02 = [
            ofpp.NXFlowSpecMatch(src=ether_types.ETH_TYPE_IP,
                                 dst=('eth_type_nxm', 0),
                                 n_bits=16),
            ofpp.NXFlowSpecLoad(src=('eth_src_nxm', 0),
                                dst=('eth_dst_nxm', 0),
                                n_bits=48),
            ofpp.NXFlowSpecOutput(src=('reg4', 0), dst='', n_bits=32),
        ]
        actions_01 = [
            ofpp.OFPActionSetField(reg4=ofport),
            ofpp.NXActionLearn(
                table_id=ext_output_table,
                priority=100,
                idle_timeout=l3_constants.DVR_LEARN_IDLE_TIMEOUT,
                specs=flow_specs_01),
            ofpp.NXActionLearn(table_id=ext_output_table,
                               priority=99,
                               specs=flow_specs_02)
        ]
        instructions_01 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_01)
        ]

        match_02 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP)
        actions_02 = [ofpp.OFPActionDecNwTtl()]
        instructions_02 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_02),
            ofpp.OFPInstructionGotoTable(table_id=ext_output_table)
        ]

        match_03 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IPV6)
        actions_03 = [ofpp.OFPActionDecNwTtl()]
        instructions_03 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_03),
            ofpp.OFPInstructionGotoTable(table_id=ext_output_table)
        ]

        self.assertEqual(self.br.install_instructions.call_args_list, [
            mock.call(table_id=input_table,
                      priority=60,
                      instructions=instructions_01,
                      match=match_01),
            mock.call(table_id=input_table,
                      priority=25,
                      instructions=instructions_02,
                      match=match_02),
            mock.call(table_id=input_table,
                      priority=25,
                      instructions=instructions_03,
                      match=match_03)
        ])
        self.assertEqual(self.br.install_drop.call_args_list, [
            mock.call(table_id=ext_output_table, priority=25),
        ])
        self.assertEqual(self.br.install_goto.call_args_list, [
            mock.call(dest_table_id=l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE,
                      table_id=snat_table,
                      priority=1)
        ])

    def test_install_router_interface_flows(self):
        ip = '**********'
        ip_cidr = '*********/24'
        port = 7
        mac = 'aa:bb:cc:dd:ee:ff'
        self.br.install_router_interface_flows(ip, ip_cidr, port, mac)
        (ofp, ofpp) = self.ofp, self.ofpp
        input_table = l3_constants.DVR_BRIDGE_INPUT_TABLE
        next_hop_table = l3_constants.DVR_BRIDGE_NEXT_HOP_TABLE
        admin_state_table = l3_constants.DVR_BRIDGE_FIP_ADMIN_STATE
        output_table = l3_constants.DVR_BRIDGE_INT_OUTPUT_TABLE
        mac_learning_table = l3_constants.DVR_BRIDGE_MAC_LEARNING_TABLE

        self.assertEqual(self.br.install_goto.call_args_list, [
            mock.call(priority=100,
                      eth_type=ether_types.ETH_TYPE_IP,
                      table_id=input_table,
                      ipv4_dst=ip_cidr,
                      dest_table_id=admin_state_table),
            mock.call(priority=50,
                      eth_type=ether_types.ETH_TYPE_ARP,
                      table_id=input_table,
                      arp_tpa=ip,
                      dest_table_id=admin_state_table)
        ])

        match_01 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_ARP,
                                 arp_op=arp.ARP_REQUEST,
                                 arp_tpa=ip,
                                 in_port=port)
        actions_01 = [
            ofpp.NXActionRegMove(src_field='eth_src',
                                 dst_field='eth_dst',
                                 n_bits=48),
            ofpp.OFPActionSetField(eth_src=mac),
            ofpp.OFPActionSetField(arp_op=arp.ARP_REPLY),
            ofpp.NXActionRegMove(src_field='arp_sha',
                                 dst_field='arp_tha',
                                 n_bits=48),
            ofpp.NXActionRegMove(src_field='arp_spa',
                                 dst_field='arp_tpa',
                                 n_bits=32),
            ofpp.OFPActionSetField(arp_spa=ip),
            ofpp.OFPActionSetField(arp_sha=mac),
            ofpp.NXActionResubmitTable(table_id=output_table),
            ofpp.NXActionResubmitTable(table_id=mac_learning_table)
        ]
        instructions_01 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_01)
        ]

        match_02 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                 ip_proto=in_proto.IPPROTO_ICMP,
                                 ipv4_dst=ip,
                                 icmpv4_type=icmpv4.ICMP_ECHO_REQUEST)
        actions_02 = [
            ofpp.NXActionRegMove(src_field='ipv4_src',
                                 dst_field='ipv4_dst',
                                 n_bits=32),
            ofpp.OFPActionSetField(ipv4_src=ip),
            ofpp.OFPActionSetField(icmpv4_type=icmpv4.ICMP_ECHO_REPLY),
            ofpp.NXActionRegMove(src_field='eth_src',
                                 dst_field='eth_dst',
                                 n_bits=48),
            ofpp.OFPActionSetField(eth_src=mac)
        ]
        instructions_02 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_02),
            ofpp.OFPInstructionGotoTable(table_id=output_table)
        ]

        match_03 = ofpp.OFPMatch(eth_type=ether_types.ETH_TYPE_IP,
                                 ip_proto=in_proto.IPPROTO_ICMP,
                                 eth_src=mac,
                                 ipv4_src=ip)
        actions_03 = [ofpp.OFPActionOutput(ofp.OFPP_IN_PORT, 0)]
        instructions_03 = [
            ofpp.OFPInstructionActions(ofp.OFPIT_APPLY_ACTIONS, actions_03)
        ]
        self.assertEqual(self.br.install_instructions.call_args_list, [
            mock.call(table_id=next_hop_table,
                      priority=300,
                      instructions=instructions_01,
                      match=match_01),
            mock.call(table_id=next_hop_table,
                      priority=300,
                      instructions=instructions_02,
                      match=match_02),
            mock.call(table_id=output_table,
                      priority=150,
                      instructions=instructions_03,
                      match=match_03)
        ])

    def test__process_external_gateway(self):
        ri = self._create_router()
        mock.patch.object(ri, 'external_gateway_added').start()
        mock.patch.object(ri, 'external_gateway_updated').start()
        mock.patch.object(ri, 'external_gateway_removed').start()
        ex_gw_port =\
            {'fixed_ips':
                [{'ip_address': '************',
                  'prefixlen': 24,
                  'subnet_id': 'mysubnet1'}],
             'id': 'my_ex_gw_port',
             'mac_address': 'aa:bb:cc:dd:ee:ff',
             'subnets': [{'cidr': '**********/24',
                          'dns_nameservers': [],
                          'gateway_ip': '**********',
                          'id': 'mysubnet1',
                          'ipv6_ra_mode': None}]}
        ri._process_external_gateway(ex_gw_port)
        self.assertEqual(1, ri.external_gateway_added.call_count)
        ri.ex_gw_port = ex_gw_port
        ri._process_external_gateway(None)
        self.assertEqual(1, ri.external_gateway_removed.call_count)

    def test_process_external(self):
        ri = self._create_router()
        mock.patch.object(ri, '_process_external_gateway').start()
        mock.patch.object(ri, 'process_floating_ip_addresses').start()
        mock.patch.object(ri, 'get_fip_agent_gateway').start()
        ri.process_external()
        self.assertEqual(1, ri._process_external_gateway.call_count)
        ri.process_floating_ip_addresses.assert_called_once_with(
            'qg-my_ex_gw_po')

    def test_get_router_cidrs(self):
        ri = self._create_router()
        result = ri.get_router_cidrs()
        self.assertEqual(result, {'**********/24'})

    def test_get_updated_ports(self):
        ri = self._create_router()

        existing_ports = [{
            'network_id':
            'my_net',
            'device_owner':
            'network:router_interface_distributed',
            'fixed_ips': [{
                'ip_address': '********',
                'subnet_id': 'my_sub1'
            }],
            'id':
            'port_foo',
            'mac_address':
            'fa:16:3e:35:89:42',
            'subnets': [{
                'cidr': '10.0.0.0/26',
                'id': 'my-sub1'
            }]
        }]

        current_ports = [{
            'fixed_ips': [{
                'ip_address': '************',
                'prefixlen': 24,
                'subnet_id': 'mysubnet1'
            }],
            'id':
            'port_foo',
            'mac_address':
            'aa:bb:cc:dd:ee:ff',
            'subnets': [{
                'cidr': '**********/24',
                'gateway_ip': '**********',
                'id': 'mysubnet1'
            }]
        }]
        result = ri._get_updated_ports(existing_ports, current_ports)
        expected = {
            'port_foo': {
                'id':
                'port_foo',
                'subnets': [{
                    'id': 'mysubnet1',
                    'cidr': '**********/24',
                    'gateway_ip': '**********'
                }],
                'fixed_ips': [{
                    'prefixlen': 24,
                    'subnet_id': 'mysubnet1',
                    'ip_address': '************'
                }],
                'mac_address':
                'aa:bb:cc:dd:ee:ff'
            }
        }
        self.assertEqual(result, expected)

    def test_process_internal_ports(self):
        ri = self._create_router()
        mock.patch.object(ri, '_get_updated_ports').start()
        mock.patch.object(ri, 'internal_network_added').start()
        mock.patch.object(ri, 'internal_network_removed').start()
        ri._process_internal_ports()
        ri.internal_network_added.assert_called_once_with({
            'network_id':
            'net-2',
            'id':
            'intf-2',
            'admin_state_up':
            True,
            'device_owner':
            'network:router_interface_distributed',
            'mac_address':
            'aa:bb:cc:dd:ee:22',
            'fixed_ips': [{
                'prefixlen': 24,
                'subnet_id': 'sub-2',
                'ip_address': '*********'
            }],
            'subnets': [{
                'cidr': '*********/24',
                'gateway_ip': '*********',
                'id': 'sub-2',
                'ipv6_ra_mode': None
            }]
        })

    def test_process_external_on_delete(self):
        ri = self._create_router()
        mock.patch.object(ri, '_process_external_gateway').start()
        mock.patch.object(ri, 'disable_floating_ip_addresses').start()
        ri._process_external_on_delete()
        ri._process_external_gateway.assert_called_once_with({
            'id':
            'my_ex_gw_port',
            'fixed_ips': [{
                'ip_address': '************',
                'subnet_id': 'my-ext-sub1',
                'prefixlen': 24
            }],
            'subnets': [{
                'id': mock.ANY,
                'gateway_ip': None,
                'cidr': '**********/24'
            }],
            'mac_address':
            'aa:bb:cc:dd:ee:ff',
            'network_id':
            'ext-net-id'
        })
        ri.disable_floating_ip_addresses.assert_called_once_with()
