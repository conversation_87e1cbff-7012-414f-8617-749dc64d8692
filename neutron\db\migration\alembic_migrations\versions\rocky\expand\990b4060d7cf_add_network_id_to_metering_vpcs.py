# Copyright 2020 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add network id to metering_vpcs

Revision ID: 990b4060d7cf
Revises: cd60ffb8ac66
Create Date: 2021-01-08 10:42:48.317736

"""

from alembic import op
from neutron_lib.db import constants as db_const
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '990b4060d7cf'
down_revision = 'cd60ffb8ac66'


def upgrade():
    table_name = 'meteringvpcs'
    existColumn = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for column in insp.get_columns(table_name):
        if column['name'] == 'network_id':
            existColumn = True

    if not existColumn:
        op.add_column(
            table_name,
            sa.Column(
                'network_id',
                sa.String(
                    length=db_const.UUID_FIELD_SIZE),
                nullable=True))
