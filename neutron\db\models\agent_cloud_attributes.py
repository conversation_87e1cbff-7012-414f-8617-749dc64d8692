#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.db import model_base
import sqlalchemy as sa
from sqlalchemy import orm

from neutron.db.models import agent


class AgentCloudAttributes(model_base.BASEV2):
    """Cloud attributes for an agent."""

    __tablename__ = "agent_cloud_attributes"
    agent_id = sa.Column(sa.String(36),
                         sa.<PERSON>ey('agents.id', ondelete="CASCADE"),
                         primary_key=True)
    cloud_attributes = sa.Column(sa.String(4095), nullable=True)
    # Add a relationship to the Agent model in order to instruct
    # SQLAlchemy to eagerly load this association
    agent = orm.relationship(agent.Agent,
                             load_on_pending=True,
                             backref=orm.backref("cloud_attributes",
                                                 lazy='joined',
                                                 uselist=False,
                                                 cascade='delete'))
