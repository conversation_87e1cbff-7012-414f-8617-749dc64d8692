# Copyright 2020 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""rename_dest_router_uuid

Revision ID: cae680a5b17d
Revises: ff15eb6acb08
Create Date: 2020-09-18 09:33:20.529438

"""

from alembic import op
from neutron_lib.db import constants as db_const
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'cae680a5b17d'
down_revision = 'ff15eb6acb08'


def upgrade():
    table_name = 'meteringvpcs'
    existColumn = 0b0
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for column in insp.get_columns(table_name):
        existColumn |= 0b1 if column['name'] == 'dest_router_uuid' else 0b0
        existColumn |= 0b10 if column['name'] == 'dest_device_uuid' else 0b00
    if existColumn & 0b1:
        op.drop_column('meteringvpcs', 'dest_router_uuid')
    if not existColumn & 0b10:
        op.add_column('meteringvpcs',
                      sa.Column('dest_device_uuid',
                                sa.String(length=db_const.IP_ADDR_FIELD_SIZE),
                                nullable=True))
