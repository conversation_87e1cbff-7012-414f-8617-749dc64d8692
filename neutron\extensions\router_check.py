#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import abc
import os

from neutron_lib.api import extensions as api_extensions
from neutron_lib.api import faults
from neutron_lib.api import validators
from neutron_lib import exceptions as n_exc
from neutron_lib.plugins import directory
from neutron_lib.services import base as service_base
import six

from neutron._i18n import _
from neutron.api import extensions
from neutron.api.v2 import resource
from neutron.extensions import _router_check as apidef
from neutron import wsgi


extensions.append_api_extensions_path([os.path.dirname(__file__)])


class Router_check(api_extensions.APIExtensionDescriptor):
    """ROUTER CHECK API extension."""

    api_definition = apidef

    @classmethod
    def get_resources(cls):
        """Returns Ext Resources."""
        exts = []

        controller = resource.Resource(RouterCheckController(),
                                       faults.FAULT_MAP)
        exts.append(extensions.ResourceExtension(
            apidef.COLLECTION_NAME, controller))

        return exts

    @classmethod
    def get_plugin_interface(cls):
        return RouterCheckPluginBase


@six.add_metaclass(abc.ABCMeta)
class RouterCheckPluginBase(service_base.ServicePluginBase):

    @classmethod
    def get_plugin_type(cls):
        return apidef.ROUTER_CHECK

    def get_plugin_description(self):
        return "Router-check Service Plugin"

    @abc.abstractmethod
    def router_check(self, context, router_id=None, host=None):
        pass

    @abc.abstractmethod
    def router_ns_info(self, context, router_id):
        pass


class RouterCheckController(wsgi.Controller):
    def create(self, request, **kwargs):
        if not request.context.is_admin:
            reason = _("Only admin is authorized "
                       "to access port check API")
            raise n_exc.AdminRequired(reason=reason)
        router_id = kwargs['body'].get('router_id', None)
        host = kwargs['body'].get('host', None)
        plugin = directory.get_plugin(apidef.ROUTER_CHECK)
        return plugin.router_check(request.context, router_id, host)

    def index(self, request, **kwargs):
        if not request.context.is_admin:
            reason = _("Only admin is authorized "
                       "to access port check API")
            raise n_exc.AdminRequired(reason=reason)
        plugin = directory.get_plugin(apidef.ROUTER_CHECK)
        kwargs = self._process_request(request, **kwargs)
        router_id = kwargs.pop('router_id')
        return plugin.router_ns_info(request.context, router_id)

    def _process_request(self, request, **kwargs):
        attr_map = apidef.RESOURCE_ATTRIBUTE_MAP.get(
            apidef.COLLECTION_NAME)
        for k, v in request.GET.items():
            if k not in attr_map:
                continue
            for rule in attr_map[k].get('validate', []):
                validator = validators.get_validator(rule)
                res = validator(v, attr_map[k].get('validate')[rule])
                if res:
                    msg_dict = dict(attr=k, reason=res)
                    msg = _("Invalid input for %(attr)s. "
                            "Reason: %(reason)s.") % msg_dict
                    raise n_exc.InvalidInput(error_message=msg)
            kwargs[k] = v
        return kwargs
