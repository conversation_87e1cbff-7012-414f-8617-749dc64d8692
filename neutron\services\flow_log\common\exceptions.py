#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron._i18n import _
from neutron_lib import exceptions as n_exc


class FlowLogNotFound(n_exc.NotFound):
    message = _("Flow Log %(id)s could not be found.")


class FlowLogCreateError(n_exc.BadRequest):
    message = _("Failed to create Flow Log, error: %(error)s.")


class FlowLogUpdateError(n_exc.BadRequest):
    message = _("Failed to update Flow Log %(id)s, error: %(error)s")


class FlowLogQuotaExceeded(n_exc.BadRequest):
    message = _("Flow Log quota limit has reached the maximum number.")


class FlowLogPortBindingError(n_exc.NeutronException):
    message = _("Flow Log binding for port %(port_id)s and flow log "
                "%(log_id)s could not be created: %(db_error)s.")


class FlowLogNetworkBindingError(n_exc.NeutronException):
    message = _("Flow Log binding for network %(net_id)s and flow log "
                "%(log_id)s could not be created: %(db_error)s.")


class FlowLogNetworkBindingNotFound(n_exc.NotFound):
    message = _("Flow Log binding for network %(net_id)s and flow log"
                " %(log_id)s could not be found.")


class FlowLogPortBindingNotFound(n_exc.NotFound):
    message = _("Flow Log binding for port %(port_id)s and flow log"
                " %(log_id)s could not be found.")


class FlowLogInUse(n_exc.InUse):
    message = _("Flow Log %(log_id)s is used by "
                "%(object_type)s %(object_id)s.")


class InvalidFlowLogResourceId(n_exc.BadRequest):
    message = _("This %(resource)s %(id)s already has "
                "flow log instance existed.")
