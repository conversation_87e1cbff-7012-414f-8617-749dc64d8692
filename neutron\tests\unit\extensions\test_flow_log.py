#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock

from neutron_lib import context
from neutron_lib.plugins import directory
from oslo_config import cfg
from oslo_utils import uuidutils

from neutron.extensions import _flow_log as api_def
from neutron.services.flow_log.common import exceptions
from neutron.services.flow_log import plugin
from neutron.tests.unit.plugins.ml2 import test_plugin

_uuid = uuidutils.generate_uuid


class FlowLogTestPlugin(plugin.FlowLogPlugin):
    supported_extension_aliases = [api_def.ALIAS]


class FlowLogExtensionTestCase(test_plugin.Ml2PluginV2TestCase):
    _extension_drivers = ['flow_log']
    flow_log_plugin = ('neutron.tests.unit.extensions.test_flow_log.'
                       'FlowLogTestPlugin')

    def get_additional_service_plugins(self):
        p = super(FlowLogExtensionTestCase,
                  self).get_additional_service_plugins()
        p.update({'flow_log_plugin_name': self.flow_log_plugin})
        return p

    def setUp(self):
        cfg.CONF.set_override('extension_drivers',
                              self._extension_drivers,
                              group='ml2')
        super(FlowLogExtensionTestCase, self).setUp()
        self.core_plugin = directory.get_plugin()
        self.plugin = directory.get_plugin(api_def.FLOW_LOG_EXTENSION)
        self.plugin.push_api = mock.Mock()

        self.ctx = context.get_admin_context()
        self.tennat_id = _uuid()

    def _create_flow_log(self, name='flow-log', collection_interval=10,
                         enabled=False,
                         traffic_type='all'):
        data = {api_def.FLOW_LOG: {'name': name,
                                   'project_id': self.tennat_id,
                                   'collection_interval': collection_interval,
                                   'enabled': enabled,
                                   'traffic_type': traffic_type}}
        return self.plugin.create_flow_log(self.ctx, data)

    def _bind_resource(self, resource_type, resource_id, flow_log_id):
        data = {resource_type: {'flow_log_id': flow_log_id}}
        return self._update('{}s'.format(resource_type), resource_id, data,
                            neutron_context=self.ctx)[resource_type]

    def test_create_flow_log(self):
        flow_log = self._create_flow_log('flow')
        self.assertEqual('flow', flow_log['name'])
        self.assertEqual(10, flow_log['collection_interval'])
        self.assertFalse(flow_log['enabled'])
        self.assertEqual('all', flow_log['traffic_type'])

    def test_update_flow_log(self):
        old_flow_log = self._create_flow_log('flow')
        self.assertEqual('flow', old_flow_log['name'])
        self.assertFalse(old_flow_log['enabled'])
        data = {api_def.FLOW_LOG: {'name': 'changed-name',
                                   'enabled': True}}
        new_flow_log = self.plugin.update_flow_log(self.ctx,
                                                   old_flow_log['id'], data)
        self.assertEqual('changed-name', new_flow_log['name'])
        self.assertTrue(new_flow_log['enabled'])

    def test_delete_flow_log(self):
        flow_log = self._create_flow_log()
        self.plugin.delete_flow_log(self.ctx, flow_log['id'])
        self.assertRaises(exceptions.FlowLogNotFound,
                          self.plugin.get_flow_log, self.ctx, flow_log['id'])

    def test_get_flow_logs(self):
        self._create_flow_log('flow_log_1')
        self._create_flow_log('flow_log_2')
        flow_logs = self.plugin.get_flow_logs(self.ctx)
        self.assertEqual(2, len(flow_logs))

    def test_get_flow_log(self):
        flow_log = self._create_flow_log()
        retrieved = self.plugin.get_flow_log(self.ctx, flow_log['id'])
        self.assertEqual(flow_log['name'], retrieved['name'])
        self.assertEqual(flow_log['collection_interval'],
                         retrieved['collection_interval'])
        self.assertEqual(flow_log['enabled'], retrieved['enabled'])
        self.assertEqual(flow_log['traffic_type'], retrieved['traffic_type'])

    def test_flow_log_bind_port(self):
        flow_log = self._create_flow_log()
        with self.port(name='port') as port:
            port_id = port['port']['id']
            port = self._bind_resource('port', port_id, flow_log['id'])
            self.assertEqual(flow_log['id'], port['flow_log_id'])

    def test_flow_log_unbound_port(self):
        flow_log = self._create_flow_log()
        with self.port(name='port') as port:
            port_id = port['port']['id']
            port = self._bind_resource('port', port_id, flow_log['id'])
            self.assertEqual(flow_log['id'], port['flow_log_id'])
            data = {'port': {'flow_log_id': None}}
            port = self._update('ports', port_id, data,
                                neutron_context=self.ctx)['port']
            self.assertIsNone(port['flow_log_id'])

    def test_flow_log_bind_network(self):
        flow_log = self._create_flow_log()
        with self.network(name='net') as network:
            network_id = network['network']['id']
            network = self._bind_resource('network', network_id,
                                          flow_log['id'])
            self.assertEqual(flow_log['id'], network['flow_log_id'])

    def test_flow_log_unbound_network(self):
        flow_log = self._create_flow_log()
        with self.network(name='net') as network:
            network_id = network['network']['id']
            network = self._bind_resource('network', network_id,
                                          flow_log['id'])
            self.assertEqual(flow_log['id'], network['flow_log_id'])
            data = {'network': {'flow_log_id': None}}
            network = self._update('networks', network_id, data,
                                   neutron_context=self.ctx)['network']
            self.assertIsNone(network['flow_log_id'])

    def test_flow_log_bound_by_many_ports(self):
        flow_log = self._create_flow_log()
        with self.port() as port_1, self.port() as port_2:
            port_1_id = port_1['port']['id']
            port_2_id = port_2['port']['id']
            port_1 = self._bind_resource('port', port_1_id, flow_log['id'])
            port_2 = self._bind_resource('port', port_2_id, flow_log['id'])
            self.assertEqual(flow_log['id'], port_1['flow_log_id'])
            self.assertEqual(flow_log['id'], port_2['flow_log_id'])

    def test_flow_log_bound_by_many_networks(self):
        flow_log = self._create_flow_log()
        with self.network('net1') as net_1, self.network('net2') as net_2:
            flow_log_id = flow_log['id']
            network_1_id = net_1['network']['id']
            network_2_id = net_2['network']['id']
            net_1 = self._bind_resource('network', network_1_id, flow_log_id)
            net_2 = self._bind_resource('network', network_2_id, flow_log_id)
            self.assertEqual(flow_log['id'], net_1['flow_log_id'])
            self.assertEqual(flow_log['id'], net_2['flow_log_id'])

    def test_delete_flow_log_in_use(self):
        flow_log = self._create_flow_log()
        with self.port() as port:
            port_id = port['port']['id']
            self._bind_resource('port', port_id, flow_log['id'])
            self.assertRaises(exceptions.FlowLogInUse,
                              self.plugin.delete_flow_log,
                              self.ctx, flow_log['id'])

    def test_port_bind_two_flow_log(self):
        flow_log_1 = self._create_flow_log()
        flow_log_2 = self._create_flow_log()
        with self.port() as port:
            port_id = port['port']['id']
            port = self._bind_resource('port', port_id, flow_log_1['id'])
            self.assertEqual(flow_log_1['id'], port['flow_log_id'])
            port = self._bind_resource('port', port_id, flow_log_2['id'])
            self.assertEqual(flow_log_2['id'], port['flow_log_id'])

    def test_network_bind_two_flow_log(self):
        flow_log_1 = self._create_flow_log()
        flow_log_2 = self._create_flow_log()
        with self.network() as net:
            net_id = net['network']['id']
            net = self._bind_resource('network', net_id, flow_log_1['id'])
            self.assertEqual(flow_log_1['id'], net['flow_log_id'])
            net = self._bind_resource('network', net_id, flow_log_2['id'])
            self.assertEqual(flow_log_2['id'], net['flow_log_id'])
