# Copyright 2024 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""modify_uniq_elastic_snat_rule

Revision ID: b04d0ab14b3d
Revises: da1d1a134ec3
Create Date: 2024-08-19 15:51:33.239910

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.engine import reflection

# revision identifiers, used by Alembic.
revision = 'b04d0ab14b3d'
down_revision = 'da1d1a134ec3'

TABLE_NAME = 'elastic_snat_rules'


def upgrade():
    inspector = reflection.Inspector.from_engine(op.get_bind())
    unique_constraints = inspector.get_unique_constraints(TABLE_NAME)

    for constraint in unique_constraints:
        op.drop_constraint(
            constraint_name=constraint['name'],
            table_name=TABLE_NAME,
            type_="unique"
        )
    op.create_unique_constraint(
        constraint_name=('uniq_elastic_snat_rules0floatingip_id0subnet_id'),
        table_name=TABLE_NAME,
        columns=['floatingip_id', 'subnet_id']
    )
    op.create_unique_constraint(
        constraint_name=('uniq_elastic_snat_rules0'
                         'floatingip_id0internal_cidr'),
        table_name=TABLE_NAME,
        columns=['floatingip_id', 'internal_cidr']
    )


def expand_drop_exceptions():
    """Drop and replace the unique constraints for table elastic_snat_rules

    Drop the existing uniq constraints and then replace them with new unique
    constraints with column ``floatingip_id``. This is needed to use drop in
    expand migration to pass test_branches.
    """

    return {
        sa.Constraint: [
                "uniq_elastic_snat_rules0router_id0subnet_id",
                "uniq_elastic_snat_rules0router_id0internal_cidr"
        ]
    }
