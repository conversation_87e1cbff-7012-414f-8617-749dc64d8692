# Copyright 2021 OpenStack Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License"); you may
# not use this file except in compliance with the License. You may obtain
# a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
# WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
# License for the specific language governing permissions and limitations
# under the License.
#

from neutron_lib.api import extensions as api_extensions
from neutron_lib.api import faults
from neutron_lib import exceptions
from neutron_lib.plugins import directory

from neutron._i18n import _
from neutron.api import extensions
from neutron.api.v2 import resource
from neutron.extensions import _port_cloud_attributes_lib as apidef
from neutron import wsgi


class PortCloudAttrsController(wsgi.Controller):

    def index(self, request, **kwargs):
        # GET /v2.0/ports/{port_id}/cloud_attributes
        plugin = directory.get_plugin()
        return plugin.get_port_cloud_attributes(
            request.context, kwargs['port_id'])

    def show(self, request, id, **kwargs):
        # GET /v2.0/ports/{port_id}/cloud_attributes/{key}
        plugin = directory.get_plugin()
        attrs = plugin.get_port_cloud_attributes(
            request.context, kwargs['port_id'])
        extra_attrs = attrs.get(apidef.COLLECTION_NAME)
        for k, v in extra_attrs.items():
            if k == id:
                return {id: v}
        raise AttributeKeyNotFound(key=id, port_id=kwargs['port_id'])

    def update_attrs(self, request, port_id, body):
        # PUT /v2.0/ports/{port_id}/cloud_attributes
        # body: {"cloud_attributes": {"key1": "value1"}}
        plugin = directory.get_plugin()
        return plugin.extend_port_cloud_attribute(
            request.context, port_id, body)

    def delete(self, request, port_id, **kwargs):
        # DELETE /v2.0/ports/{port_id}/cloud_attributes/{key}
        plugin = directory.get_plugin()
        plugin.remove_port_cloud_attribute(
            request.context, port_id, kwargs['id'])

    def delete_attrs(self, request, port_id, body=None):
        # DELETE /v2.0/ports/{port_id}/cloud_attributes
        # body: {"cloud_attributes": {"key1": "value1"}}
        plugin = directory.get_plugin()
        port = plugin.get_port(request.context, port_id)
        if not body:
            # Delete port all cloud attributes
            for key in port[apidef.COLLECTION_NAME].keys():
                plugin.delete_port_cloud_attribute(request.context, port, key)
        else:
            # Delete port part cloud attributes
            plugin.remove_port_cloud_attributes(
                request.context, port_id, body)


class Port_cloud_attributes(api_extensions.APIExtensionDescriptor):
    """Port Cloud Attributes of Service Extension."""
    api_definition = apidef

    @classmethod
    def get_name(cls):
        return apidef.NAME

    @classmethod
    def get_alias(cls):
        return apidef.ALIAS

    @classmethod
    def get_description(cls):
        return apidef.DESCRIPTION

    @classmethod
    def get_updated(cls):
        return apidef.UPDATED_TIMESTAMP

    @classmethod
    def get_resources(cls):
        action_status = {'delete': 204, 'delete_attrs': 204,
                         'update_attrs': 200}
        controller = resource.Resource(PortCloudAttrsController(),
                                       faults.FAULT_MAP,
                                       action_status=action_status)
        collection_methods = {"delete_attrs": "DELETE",
                              "update_attrs": "PUT"}
        parent = dict(member_name="port",
                      collection_name="ports")
        ext = extensions.ResourceExtension(
            apidef.COLLECTION_NAME, controller, parent,
            collection_methods=collection_methods)
        return [ext]


class AttributeKeyNotFound(exceptions.NotFound):
    message = _("Key %(key)s not found in port %(port_id)s cloud attributes.")


class AttributeValueNotFound(exceptions.NotFound):
    message = _("Delete values include not found value in key %(key)s's "
                "current values.")


class PortCloudAttributeNotFound(exceptions.NotFound):
    message = _("%(port_id)s cloud attributes not found.")
