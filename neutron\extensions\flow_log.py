#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import abc

from neutron_lib.api import extensions as api_extensions
from neutron_lib.plugins import directory
from neutron_lib.services import base as service_base
import six

from neutron.api import extensions
from neutron.api.v2 import base
from neutron.extensions import _flow_log as apidef
from neutron.extensions import standardattrdescription as ext_stddesc


class Flow_log(api_extensions.ExtensionDescriptor):
    """Flow log extension."""

    @classmethod
    def get_name(cls):
        return apidef.NAME

    @classmethod
    def get_alias(cls):
        return apidef.ALIAS

    @classmethod
    def get_description(cls):
        return apidef.DESCRIPTION

    @classmethod
    def get_updated(cls):
        return apidef.UPDATED_TIMESTAMP

    @classmethod
    def get_resources(cls):
        """Returns Ext Resources."""
        resource_attributes = apidef.RESOURCE_ATTRIBUTE_MAP[apidef.FLOW_LOGS]
        controller = base.create_resource(
            apidef.FLOW_LOGS,
            apidef.FLOW_LOG,
            directory.get_plugin(apidef.FLOW_LOG_EXTENSION),
            resource_attributes,
            allow_pagination=True,
            allow_sorting=True)
        return [extensions.ResourceExtension(apidef.FLOW_LOGS,
                                             controller,
                                             attr_map=resource_attributes)]

    def update_attributes_map(self, attributes):
        super(Flow_log, self).update_attributes_map(
            attributes, extension_attrs_map=apidef.RESOURCE_ATTRIBUTE_MAP)

    def get_extended_resources(self, version):
        if version == "2.0":
            return apidef.RESOURCE_ATTRIBUTE_MAP
        else:
            return {}

    def get_required_extensions(self):
        return [ext_stddesc.Standardattrdescription.get_alias()]


@six.add_metaclass(abc.ABCMeta)
class FlowLogPluginBase(service_base.ServicePluginBase):
    @classmethod
    def get_plugin_type(cls):
        return apidef.FLOW_LOG_EXTENSION

    def get_plugin_description(self):
        return 'Flow Log Service Plugin'

    @abc.abstractmethod
    def create_flow_log(self, context, flow_log):
        pass

    @abc.abstractmethod
    def update_flow_log(self, context, id, flow_log):
        pass

    @abc.abstractmethod
    def delete_flow_log(self, context, id):
        pass

    @abc.abstractmethod
    def get_flow_log(self, context, id, fields=None):
        pass

    @abc.abstractmethod
    def get_flow_logs(self, context, filters=None, fields=None,
                      sorts=None, limit=None, marker=None,
                      page_reverse=False):
        pass
