# Copyright 2024 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add cloud attributes to subnet

Revision ID: da1d1a134ec3
Revises: ae9ebf736339
Create Date: 2024-05-12 15:20:33.410620

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'da1d1a134ec3'
down_revision = 'ae9ebf736339'


def upgrade():
    exist_subnet_cloud_attributes = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for table in insp.get_table_names():
        if table == 'subnet_cloud_attributes':
            exist_subnet_cloud_attributes = True
            break
    if not exist_subnet_cloud_attributes:
        op.create_table(
            'subnet_cloud_attributes',
            sa.Column('subnet_id', sa.String(length=36), nullable=False),
            sa.Column('cloud_attributes', sa.String(4095)),
            sa.ForeignKeyConstraint(['subnet_id'], ['subnets.id'],
                                    ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('subnet_id')
        )
