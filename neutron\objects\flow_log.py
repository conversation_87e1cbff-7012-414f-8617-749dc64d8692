#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.


from oslo_db import exception as db_exc
from oslo_versionedobjects import fields as obj_fields

from neutron.db.models import flow_log as models
from neutron.objects import base
from neutron.objects import common_types
from neutron.objects.db import api as obj_db_api
from neutron.services.flow_log.common import exceptions


@base.NeutronObjectRegistry.register
class FlowLogPortBinding(base.NeutronDbObject):
    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = models.FlowLogPortBinding

    fields = {
        'flow_log_id': common_types.UUIDField(),
        'port_id': common_types.UUIDField(),
    }

    primary_keys = ['port_id']
    fields_no_update = ['flow_log_id', 'port_id']


@base.NeutronObjectRegistry.register
class FlowLogNetworkBinding(base.NeutronDbObject):
    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = models.FlowLogNetworkBinding

    fields = {
        'flow_log_id': common_types.UUIDField(),
        'network_id': common_types.UUIDField(),
    }

    primary_keys = ['network_id']
    fields_no_update = ['flow_log_id', 'network_id']


@base.NeutronObjectRegistry.register
class FlowLog(base.NeutronDbObject):
    VERSION = '1.0'

    db_model = models.FlowLog

    fields = {
        'id': obj_fields.UUIDField(),
        'project_id': obj_fields.StringField(),
        'name': obj_fields.StringField(nullable=True),
        'collection_interval': obj_fields.IntegerField(nullable=True),
        'enabled': obj_fields.BooleanField(nullable=True),
        # 'priority': obj_fields.IntegerField(nullable=True),
        # 'resource_id': obj_fields.UUIDField(nullable=True),
        # 'resource_type':common_types.FlowLogResourceTypeField(nullable=True),
        'traffic_type': common_types.FlowLogTrafficTypeField(nullable=False),
    }

    # synthetic_fields = ['resource_id', 'resource_type']
    fields_no_update = ['project_id', 'id', 'collection_interval',
                        'traffic_type']
    binding_models = {'port': FlowLogPortBinding,
                      'network': FlowLogNetworkBinding}

    @classmethod
    def get_flow_log_obj(cls, context, flow_log_id):
        """Fetch a Flow log.

        :param context: neutron api request context
        :type context: neutron.context.Context
        :param flow_log_id: the id of the QosPolicy to fetch
        :type flow_log_id: str uuid

        :returns: a FlowLog object
        :raises: n_exc.FlowLogNotFound
        """

        obj = cls.get_object(context, id=flow_log_id)
        if obj is None:
            raise exceptions.FlowLogNotFound(id=flow_log_id)
        return obj

    @classmethod
    def _get_object_flow_log(cls, context, binding_cls, **kwargs):
        with cls.db_context_reader(context):
            binding_obj = obj_db_api.get_object(binding_cls, context,
                                                **kwargs)
            if binding_obj:
                return cls.get_object(context, id=binding_obj['flow_log_id'])

    @classmethod
    def get_network_flow_log(cls, context, network_id):
        return cls._get_object_flow_log(context, FlowLogNetworkBinding,
                                        network_id=network_id)

    @classmethod
    def get_port_flow_log(cls, context, port_id):
        return cls._get_object_flow_log(context, FlowLogPortBinding,
                                      port_id=port_id)

    def attach_network(self, network_id):
        try:
            FlowLogNetworkBinding(self.obj_context,
                                  network_id=network_id,
                                  flow_log_id=self.id).create()
        except db_exc.DBReferenceError as e:
            raise exceptions.FlowLogNetworkBindingError(log_id=self.id,
                                                        net_id=network_id,
                                                        db_error=e)

    def attach_port(self, port_id):
        try:
            FlowLogPortBinding(self.obj_context,
                               port_id=port_id,
                               flow_log_id=self.id).create()
        except db_exc.DBReferenceError as e:
            raise exceptions.FlowLogPortBindingError(log_id=self.id,
                                                     port_id=port_id,
                                                     db_error=e)

    def detach_network(self, network_id):
        deleted = FlowLogNetworkBinding.delete_objects(
            self.obj_context, network_id=network_id)
        if not deleted:
            raise exceptions.FlowLogNetworkBindingNotFound(net_id=network_id,
                                                           log_id=self.id)

    def detach_port(self, port_id):
        deleted = FlowLogPortBinding.delete_objects(self.obj_context,
                                                    port_id=port_id)
        if not deleted:
            raise exceptions.FlowLogPortBindingNotFound(port_id=port_id,
                                                        log_id=self.id)

    def get_bound_networks(self):
        return [
            nb.network_id
            for nb in FlowLogNetworkBinding.get_objects(
                self.obj_context, flow_log_id=self.id)
        ]

    def get_bound_ports(self):
        return [
            pb.port_id
            for pb in FlowLogPortBinding.get_objects(
                self.obj_context, flow_log_id=self.id)
        ]
