# Copyright 2024 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add cloud attributes to agent

Revision ID: eb4819f04fb2
Revises: f8c41b97e3ee
Create Date: 2024-09-10 16:00:47.061132

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'eb4819f04fb2'
down_revision = 'f8c41b97e3ee'


def upgrade():
    exist_agent_cloud_attributes = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for table in insp.get_table_names():
        if table == 'agent_cloud_attributes':
            exist_agent_cloud_attributes = True
            break
    if not exist_agent_cloud_attributes:
        op.create_table(
            'agent_cloud_attributes',
            sa.Column('agent_id', sa.String(length=36), nullable=False),
            sa.Column('cloud_attributes', sa.String(4095)),
            sa.ForeignKeyConstraint(
                ['agent_id'], ['agents.id'], ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('agent_id')
        )
