# Copyright 2021 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add_meter_type_multiple

Revision ID: 25f18202f2f9
Revises: cd9ef14ccf87
Create Date: 2021-08-31 15:23:42.287123

"""

from alembic import op
import sqlalchemy as sa

from neutron.common import constants as n_const
from neutron.db import migration

# revision identifiers, used by Alembic.
revision = '25f18202f2f9'
down_revision = 'cd9ef14ccf87'


new_l_type_enum = sa.Enum(n_const.METERING_TYPE_EIP,
                          n_const.METERING_TYPE_PF_EIP,
                          n_const.METERING_TYPE_SNAT_EIP,
                          n_const.METERING_TYPE_ECS_EIP,
                          n_const.METERING_TYPE_MULTI_EIP)


r_type_enum = sa.Enum(n_const.METERING_TYPE_EIP,
                      n_const.METERING_TYPE_PF_EIP,
                      n_const.METERING_TYPE_SNAT_EIP,
                      n_const.METERING_TYPE_ECS_EIP)


def upgrade():
    table_name = 'meteringlabels'
    labelExistColumn = False
    ruleExistColumn = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)
    for column in insp.get_columns(table_name):
        if column['name'] == 'type':
            labelExistColumn = True
    if not labelExistColumn:
        op.add_column(table_name, sa.Column(
            'type', new_l_type_enum, nullable=False,
            server_default=n_const.METERING_TYPE_EIP))
    migration.alter_enum_add_value(table_name, 'type', new_l_type_enum, False,
                                   server_default=n_const.METERING_TYPE_EIP)

    rule_name = 'meteringlabelrules'
    for column in insp.get_columns(rule_name):
        if column['name'] == 'type':
            ruleExistColumn = True
    if ruleExistColumn:
        op.drop_column(rule_name, 'type')
    op.add_column(rule_name, sa.Column(
        'type', r_type_enum, nullable=False,
        server_default=n_const.METERING_TYPE_EIP))
