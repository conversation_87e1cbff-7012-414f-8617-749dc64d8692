# Copyright 2024 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""modify uniq port forwarding

Revision ID: f8c41b97e3ee
Revises: b04d0ab14b3d
Create Date: 2024-08-23 10:26:25.875314

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.engine import reflection

# revision identifiers, used by Alembic.
revision = 'f8c41b97e3ee'
down_revision = 'b04d0ab14b3d'

TABLE_NAME = 'portforwardings'


def upgrade():
    inspector = reflection.Inspector.from_engine(op.get_bind())
    constraint_to_delete = ('uniq_port_forwardings0ptcl0in_prt_id0'
                            'in_ip_addr0in_prts')
    unique_constraints = inspector.get_unique_constraints(TABLE_NAME)
    for constraint in unique_constraints:
        if constraint_to_delete == constraint['name']:
            op.drop_constraint(
                constraint_name=constraint_to_delete,
                table_name=TABLE_NAME,
                type_="unique"
            )


def expand_drop_exceptions():
    """Drop and replace the unique constraints for table portforwardings

    Drop the existing portforwardings foreign key uniq constraints and then
    replace them with new unique constraints with column ``protocol``.
    This is needed to use drop in expand migration to pass test_branches.
    """

    return {
        sa.Constraint: [
            "uniq_port_forwardings0ptcl0in_prt_id0in_ip_addr0in_prts"
        ]
    }
